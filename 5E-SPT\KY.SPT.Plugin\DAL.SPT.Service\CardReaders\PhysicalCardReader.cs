using COM.IFP.Common;
using COM.IFP.Device;
using COM.IFP.Log;
using System;
using System.Threading;


namespace DAL.SPT.Service.CardReaders
{
    /// <summary>
    /// 物理读卡器类，负责处理物理读卡器的读卡逻辑
    /// </summary>
    public class PhysicalCardReader
    {
        #region 私有字段

        /// <summary>
        /// 读卡重试次数
        /// </summary>
        private const int ReadRetryCount = 3;

        /// <summary>
        /// 读卡重试间隔(毫秒)
        /// </summary>
        private const int ReadRetryDelay = 100;

        #endregion

        #region 公共方法

        /// <summary>
        /// 读卡
        /// </summary>
        /// <param name="ip">读卡器IP地址</param>
        /// <param name="port">读卡器端口</param>
        /// <returns>读取到的卡号，如果没有读到卡返回空字符串</returns>
        public static string ReadCard(string ip, int port)
        {
            var reader = new Reader();
            try
            {
                // 尝试连接读卡器
                reader.Connect(ip, port);
                
                // 多次尝试读卡，提高成功率
                for (int i = 0; i < ReadRetryCount; i++)
                {
                    try
                    {
                        string cardNo = reader.ReadCardNo();
                        if (!string.IsNullOrEmpty(cardNo))
                            return cardNo;
                    }
                    catch
                    {
                        // 读卡失败，短暂延迟后重试
                        Thread.Sleep(ReadRetryDelay);
                    }
                }
                
                return string.Empty;
            }
            catch (Exception ex)
            {
                LogUtility.ToError($"物理读卡失败: {ip}:{port}", 2, ex);
                return string.Empty;
            }
            finally
            {
                // 确保读卡器连接被关闭
                try { reader?.Close(); } catch { /* 忽略关闭时的异常 */ }
            }
        }

        /// <summary>
        /// 检查设备是否就绪
        /// </summary>
        /// <returns>设备就绪返回true，否则返回false</returns>
        public static bool CheckDeviceReady()
        {
            // 检查PLC连接状态和数据内存
            if (!Global.PlcIsConnected || Global.DataMemory == null)
                return false;

            // 检查设备就绪状态点位
            if (!Global.CheckPointValue("01" + Constant.ZBJXXH, true))
                return false;

            // 检查自动运行命令点位
            if (!Global.CheckPointValue("01" + Constant.ZDYXML, true))
                return false;

            return true;
        }

        /// <summary>
        /// 检查站点是否允许读卡
        /// </summary>
        /// <param name="stationCode">站点编码</param>
        public static bool CheckStationReadyForCard(string stationCode)
        {
            try
            {
                if (string.IsNullOrEmpty(stationCode) || Global.DataMemory == null)
                    return false;

                // 存查样柜（6开头）没有物料检测状态点，直接允许读卡
                if (stationCode.StartsWith("6"))
                    return true;

                // 检查物料检测状态点位
                if (!Global.CheckPointValue(stationCode + Constant.YPJC, true))
                    return false;

                // 人工双向发送站（7开头）需要特殊处理
                if (stationCode.StartsWith("7"))
                {
                    // 检查允许读卡状态点位
                    if (!Global.CheckPointValue(stationCode + Constant.YXDKZT, true))
                    {
                        LogUtility.ToError($"人工站{stationCode}有瓶，请及时取走。", 2);
                        return false;
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                LogUtility.ToError($"检查站点{stationCode}读卡状态异常", 2, ex);
                return false;
            }
        }

        /// <summary>
        /// 解析读卡器地址配置
        /// </summary>
        /// <param name="address">地址配置字符串</param>
        /// <returns>解析结果，包含IP和端口</returns>
        public static (string ip, int port, bool isValid) ParseAddress(string address)
        {
            if (string.IsNullOrEmpty(address))
                return (string.Empty, 0, false);

            var parts = address.Split(':');
            if (parts.Length != 2 || !int.TryParse(parts[1], out int port))
                return (string.Empty, 0, false);

            return (parts[0], port, true);
        }

        /// <summary>
        /// 判断是否为物理读卡器地址配置
        /// </summary>
        /// <param name="address">地址配置</param>
        /// <returns>是物理读卡器返回true，否则返回false</returns>
        public static bool IsPhysicalReaderAddress(string address)
        {
            if (string.IsNullOrEmpty(address))
                return false;

            // 物理读卡器地址格式：ip:port，且不以特殊标识开头
            return !address.StartsWith("{") && 
                   !address.StartsWith("server=") && 
                   ParseAddress(address).isValid;
        }

        #endregion
    }
}
