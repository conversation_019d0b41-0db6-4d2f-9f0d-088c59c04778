using COM.IFP.Common;
using ORM.SPT;
using ORM.SPT.www;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using COM.IFP.SqlSugarN;
using SqlSugar;

namespace DAL.SPT.Service.PipelineProgress
{
    /// <summary>
    /// 传瓶进度服务 TODO：考虑加入缓存过期机制
    /// </summary>
    public class PipelineProgressService
    {
        #region 成员变量
        /// <summary>
        /// 路径缓存，避免重复计算
        /// </summary>
        private static readonly Dictionary<string, List<TaskStationData>> _pathCache = new Dictionary<string, List<TaskStationData>>();
        
        /// <summary>
        /// 缓存锁
        /// </summary>
        private static readonly object _cacheLock = new object();
        #endregion

        /// <summary>
        /// 构造函数
        /// </summary>
        public PipelineProgressService()
        {
        }



        /// <summary>
        /// 获取传瓶进度数据
        /// </summary>
        public PipelineProgressData GetPipelineProgressData()
        {
            try
            {
                // 1. 获取当前正在执行的任务
                SPT_COMMAND currentTask;
                using (var db = DB.Create())
                {
                    // 查询状态为1（执行中）的任务
                    currentTask = db.Queryable<SPT_COMMAND>()
                                    .Where(t => t.STATUS == 1)
                                    .OrderBy(t => t.START_TIME)
                                    .First();
                }
                //没有任务执行 创建空任务列表过去
                if (currentTask == null)
                {
                    return new PipelineProgressData
                    {
                        taskData = new List<TaskStationData>()
                    };
                }

                // 2. 构建传瓶进度数据
                return BuildAdvancedStationPath(currentTask);
            }
            catch (Exception ex)
            {
                LogUtility.ToError("获取传瓶进度数据失败", ex);
                return new PipelineProgressData
                {
                    taskData = new List<TaskStationData>()
                };
            }
        }

        /// <summary>
        /// 构建改进的站点路径（最远路径）
        /// </summary>
        private PipelineProgressData BuildAdvancedStationPath(SPT_COMMAND task)
        {
            var progressData = new PipelineProgressData
            {
                taskData = new List<TaskStationData>()
            };

            try
            {
                string originStation = task.ORIGIN.Value;
                string targetStation = task.TARGET.Value;
                string cacheKey = $"{originStation}-{targetStation}";

                // 1. 尝试从缓存获取路径
                List<TaskStationData> maxPath = null;
                lock (_cacheLock)
                {
                    if (_pathCache.TryGetValue(cacheKey, out var cachedPath))
                    {
                        maxPath = cachedPath.Select(s => new TaskStationData
                        {
                            STATION = s.STATION,
                            Name = s.Name,
                            IsOrNotFinish = false, // 重新计算状态
                            Sort = s.Sort
                        }).ToList();
                    }
                }

                // 2. 如果缓存中没有，计算最远路径
                if (maxPath == null)
                {
                    maxPath = CalculateMaxPath(originStation, targetStation);
                    
                    // 缓存路径（只缓存结构，不缓存状态）
                    lock (_cacheLock)
                    {
                        _pathCache[cacheKey] = maxPath.Select(s => new TaskStationData
                        {
                            STATION = s.STATION,
                            Name = s.Name,
                            IsOrNotFinish = false,
                            Sort = s.Sort
                        }).ToList();
                    }
                }

                // 3. 动态更新站点完成状态
                UpdateStationStates(maxPath);

                progressData.taskData = maxPath;
            }
            catch (Exception ex)
            {
                LogUtility.ToError("构建改进站点路径失败", ex);
                // 异常时使用原有的基础路径方法
                try
                {
                    var basicPath = BuildStationPath(task);
                    progressData.taskData = basicPath.taskData;
                    LogUtility.ToNotice($"降级使用基础路径：{task.ORIGIN.Value} -> {task.TARGET.Value}", 2);
                }
                catch (Exception fallbackEx)
                {
                    LogUtility.ToError("基础路径也失败，使用极简路径", 2, fallbackEx);
                    progressData.taskData = new List<TaskStationData>
                    {
                        new TaskStationData { STATION = task.ORIGIN.Value, Name = GetStationName(task.ORIGIN.Value), IsOrNotFinish = true, Sort = 0 },
                        new TaskStationData { STATION = task.TARGET.Value, Name = GetStationName(task.TARGET.Value), IsOrNotFinish = false, Sort = 1 }
                    };
                }
            }

            return progressData;
        }

        /// <summary>
        /// 计算最远路径 - 基于基础路径增量添加站点
        /// </summary>
        private List<TaskStationData> CalculateMaxPath(string origin, string target)
        {
            try
            {
                // 1. 先获取基础路径（起点 + 换向器 + 终点）
                var basicPath = BuildStationPath(origin, target);
                var maxPath = new List<TaskStationData>(basicPath.taskData);

                // 2. 获取基础路径中的换向器作为关键换向器
                var criticalDividers = maxPath
                    .Where(s => s.STATION != origin && s.STATION != target)
                    .Select(s => s.STATION)
                    .ToList();

                // 3. 增量添加：必需风机站（第二优先级）
                var requiredFanStations = FindRequiredFanStations(origin, target, criticalDividers);
                int insertIndex = maxPath.Count - 1; // 在终点前插入
                
                foreach (var fanStation in requiredFanStations)
                {
                    if (!maxPath.Any(s => s.STATION == fanStation))
                    {
                        maxPath.Insert(insertIndex, new TaskStationData
                        {
                            STATION = fanStation,
                            Name = GetStationName(fanStation),
                            IsOrNotFinish = false,
                            Sort = -1 
                        });
                        insertIndex++;
                    }
                }

                // 4. 增量添加：备选换向器（第三优先级）
                var alternateDividers = FindAlternateDividers(origin, target, criticalDividers);
                foreach (var divider in alternateDividers.Take(2)) // 限制备选数量 TODO 结合实际考虑是否限制
                {
                    if (!maxPath.Any(s => s.STATION == divider))
                    {
                        maxPath.Insert(insertIndex, new TaskStationData
                        {
                            STATION = divider,
                            Name = GetStationName(divider),
                            IsOrNotFinish = false,
                            Sort = -1 
                        });
                        insertIndex++;
                    }
                }

                // 5. 统一设置Sort排序（只在这里设置一次）
                for (int i = 0; i < maxPath.Count; i++)
                {
                    maxPath[i].Sort = i;
                }

                LogUtility.ToNotice($"基于基础路径扩展最远路径：{origin} -> {target}，共{maxPath.Count}个站点", 2);
                return maxPath;
            }
            catch (Exception ex)
            {
                LogUtility.ToError("计算最远路径失败", 2, ex);
                // 返回基础路径 - 调用原有方法
                try
                {
                    var basicPath = BuildStationPath(origin, target);
                    // 设置排序
                    for (int i = 0; i < basicPath.taskData.Count; i++)
                    {
                        basicPath.taskData[i].Sort = i;
                    }
                    return basicPath.taskData;
                }
                catch (Exception fallbackEx)
                {
                    LogUtility.ToError("基础路径计算也失败，返回极简路径", fallbackEx);
                    return new List<TaskStationData>
                    {
                        new TaskStationData { STATION = origin, Name = GetStationName(origin), IsOrNotFinish = true, Sort = 0 },
                        new TaskStationData { STATION = target, Name = GetStationName(target), IsOrNotFinish = false, Sort = 1 }
                    };
                }
            }
        }
        
        /// <summary>
        /// 查找必需的风机站（第二优先级）
        /// </summary>
        private List<string> FindRequiredFanStations(string origin, string target, List<string> criticalDividers)
        {
            var fanStations = new List<string>();
            
            try
            {
                // 策略：在能走通终点的关键换向器中查找风机站
                if (criticalDividers.Count > 0)
                {
                    // 拿到换向器规则
                    var allDividers = Global.DB_Option.Value.SelectAllDivider();
                    
                    // 在关键换向器中依次查找风机站
                    foreach (var dividerStation in criticalDividers)
                    {
                        var divider = allDividers.FirstOrDefault(d => d.STATION == dividerStation);
                        if (divider != null)
                        {
                            // 获取该换向器连接的所有管道（出口）
                            var dividerPipes = new[] { 
                                divider.PIPE1.Value, divider.PIPE2.Value, 
                                divider.PIPE3.Value, divider.PIPE4.Value 
                            }.Where(p => !string.IsNullOrEmpty(p));

                            // 直接从连接的管道中找风机站（以"1"开头）
                            var connectedFanStation = dividerPipes.FirstOrDefault(pipe => 
                                pipe.StartsWith("1"));

                            if (!string.IsNullOrEmpty(connectedFanStation))
                            {
                                fanStations.Add(connectedFanStation);
                                LogUtility.ToNotice($"在关键换向器{dividerStation}找到风机站：{connectedFanStation}", 2);
                                break; // 找到第一个就足够了
                            }
                        }
                    }
                    
                    // 查找失败的处理
                    if (fanStations.Count == 0)
                    {
                        LogUtility.ToNotice($"在关键路径换向器中未找到风机站，跳过风机站添加", 2);
                    }
                }
            }
            catch (Exception ex)
            {
                LogUtility.ToError("查找必需风机站失败", ex);
            }

            return fanStations;
        }



        /// <summary>
        /// 查找备选换向器（第三优先级）
        /// </summary>
        private List<string> FindAlternateDividers(string origin, string target, List<string> criticalDividers)
        {
            var alternateDividers = new List<string>();
            
            try
            {
                var allDividers = Global.DB_Option.Value.SelectAllDivider();
                
                // 查找未被包含在关键路径中的换向器
                foreach (var divider in allDividers)
                {
                    if (!criticalDividers.Contains(divider.STATION.Value))
                    {
                        // 检查是否可能作为备用路径
                        var dividerPipes = new[] { divider.PIPE0.Value, divider.PIPE1.Value, 
                                                  divider.PIPE2.Value, divider.PIPE3.Value, divider.PIPE4.Value }
                                                  .Where(p => !string.IsNullOrEmpty(p));

                        // 如果这个换向器连接了起点或终点相关的站点，作为备选
                        if (dividerPipes.Any(p => criticalDividers.Any(c => IsStationRelated(p, c))))
                        {
                            alternateDividers.Add(divider.STATION.Value);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogUtility.ToError("查找备选换向器失败", ex);
            }

            return alternateDividers;
        }

        /// <summary>
        /// 检查站点是否相关
        /// </summary>
        private bool IsStationRelated(string station1, string station2)
        {
            // 简单实现：检查是否是同一个站点或相邻站点
            return station1 == station2 || Math.Abs(int.Parse(station1.Substring(1)) - int.Parse(station2.Substring(1))) <= 1;
        }



        /// <summary>
        /// 动态更新站点状态
        /// </summary>
        private void UpdateStationStates(List<TaskStationData> stations)
        {
            try
            {
                if (stations == null || stations.Count == 0) return;

                // 起点永远标记为已完成
                stations[0].IsOrNotFinish = true;

                // 检查每个站点的完成状态
                for (int i = 1; i < stations.Count; i++)
                {
                    var station = stations[i];
                    
                    // 检查站点是否故障
                    if (IsStationFaulty(station.STATION))
                    {
                        LogUtility.ToError($"站点{station.STATION}({station.Name})检测到故障，跳过此站点", 2);
                        continue;
                    }

                    // 检查站点是否已完成
                    bool isFinished = CheckStationFinished(station.STATION);
                    station.IsOrNotFinish = isFinished;

                    // 如果发现瓶子跳过了某些站点到达了这个站点
                    if (isFinished)
                    {
                        // 将此站点之前的所有未完成站点标记为已完成
                        for (int j = 0; j < i; j++)
                        {
                            if (!stations[j].IsOrNotFinish)
                            {
                                stations[j].IsOrNotFinish = true;
                                LogUtility.ToNotice($"瓶子跳过站点{stations[j].STATION}({stations[j].Name})，自动标记为已完成", 2);
                            }
                        }
                        break; // 找到当前位置后停止检查
                    }
                }
            }
            catch (Exception ex)
            {
                LogUtility.ToError("动态更新站点状态失败", ex);
            }
        }

        /// <summary>
        /// 检查站点是否故障
        /// </summary>
        private bool IsStationFaulty(string stationCode)
        {
            try
            {
                if (Global.DataMemory == null) return false;

                // 检查通讯中断状态
                if (Global.CheckPointValue(stationCode + "TXZDZT", true))
                {
                    return true;
                }

                // 检查设备故障状态（如果有）
                if (Global.CheckPointValue(stationCode + "SBGZZT", true))
                {
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                LogUtility.ToError($"检查站点{stationCode}故障状态失败", ex);
                return false; // 异常时假设站点正常
            }
        }

        /// <summary>
        /// 获取站点名称
        /// </summary>
        /// <param name="stationCode"></param>
        /// <returns></returns>
        private string GetStationName(string stationCode)
        {
            try
            {
                using (var db = DB.Create())
                {
                    var station = db.Queryable<SPT_STATION>().Where(s => s.CODE == stationCode).First();
                    if (station != null && !string.IsNullOrEmpty(station.NAME.Value))
                    {
                        return station.NAME.Value;
                    }
                }
            }
            catch (Exception ex)
            {
                LogUtility.ToError($"获取站点{stationCode}名称失败", ex);
            }
            return stationCode;
        }

        /// <summary>
        /// 检查站点是否已完成
        /// </summary>
        private bool CheckStationFinished(string stationCode)
        {
            try
            {
                // 检查站点的物料检测状态点位，判断物料是否已经通过该站点
                // 物料检测状态
                return Global.CheckPointValue(stationCode + "WLJCZT", true);
            }
            catch (Exception ex)
            {
                LogUtility.ToError($"检查站点{stationCode}完成状态失败", ex);
                return false;
            }
        }



        /// <summary>
        /// 构建站点路径（保留原方法作为备用）
        /// </summary>
        private PipelineProgressData BuildStationPath(SPT_COMMAND task)
        {
            var result = BuildStationPath(task.ORIGIN.Value, task.TARGET.Value);
            
            // 确保Sort字段正确设置（当直接调用此方法时）
            for (int i = 0; i < result.taskData.Count; i++)
            {
                result.taskData[i].Sort = i;
            }
            
            return result;
        }

        /// <summary>
        /// 构建基础站点路径
        /// </summary>
        private PipelineProgressData BuildStationPath(string originStation, string targetStation)
        {
            var progressData = new PipelineProgressData
            {
                taskData = new List<TaskStationData>()
            };
            try
            {
                // 1. 添加起点（起点永远是第一站 也是完成状态）
                progressData.taskData.Add(new TaskStationData
                {
                    STATION = originStation,
                    Name = GetStationName(originStation),
                    IsOrNotFinish = true,
                    Sort = -1 
                });
                
                // 2. 找到换向器规则
                var allDividers = Global.DB_Option.Value.SelectAllDivider();
                // 由于一个站点只能出现在 一个换向器规则上 所以可以方向使用FirstOrDefault
                var originDivider = allDividers.FirstOrDefault(d => 
                    d.PIPE0 == originStation || 
                    d.PIPE1 == originStation || 
                    d.PIPE2 == originStation || 
                    d.PIPE3 == originStation || 
                    d.PIPE4 == originStation);
                
                // 查找与终点站相关的换向器
                var targetDivider = allDividers.FirstOrDefault(d => 
                    d.PIPE0 == targetStation || 
                    d.PIPE1 == targetStation || 
                    d.PIPE2 == targetStation || 
                    d.PIPE3 == targetStation || 
                    d.PIPE4 == targetStation);
                
                if (originDivider == null || targetDivider == null)
                {
                    LogUtility.ToError($"未找到与起点站或终点站关联的换向器", 2);
                    return progressData;
                }
                
                // 3. 根据规则添加换向器站点
                if (originDivider.STATION.Value == targetDivider.STATION.Value)
                {
                    // 添加换向器站点
                    progressData.taskData.Add(new TaskStationData
                    {
                        STATION = originDivider.STATION.Value,
                        Name = GetStationName(originDivider.STATION.Value),
                        IsOrNotFinish = CheckStationFinished(originDivider.STATION.Value),
                        Sort = -1 
                    });
                }
                else
                {
                    // 如果没有共同的换向器，需要经过两个换向器
                    // 添加起点相关的换向器
                    progressData.taskData.Add(new TaskStationData
                    {
                        STATION = originDivider.STATION.Value,
                        Name = GetStationName(originDivider.STATION.Value),
                        IsOrNotFinish = CheckStationFinished(originDivider.STATION.Value),
                        Sort = -1 
                    });
                    
                    progressData.taskData.Add(new TaskStationData
                    {
                        STATION = targetDivider.STATION.Value,
                        Name = GetStationName(targetDivider.STATION.Value),
                        IsOrNotFinish = CheckStationFinished(targetDivider.STATION.Value),
                        Sort = -1 
                    });
                }
                
                // 4. 添加终点站（终点站永远是最后一站）
                progressData.taskData.Add(new TaskStationData
                {
                    STATION = targetStation,
                    Name = GetStationName(targetStation),
                    IsOrNotFinish = CheckStationFinished(targetStation),
                    Sort = -1 
                });
            }
            catch (Exception ex)
            {
                LogUtility.ToError("构建站点路径失败", ex);
            }
            
            return progressData;
        }
    }
} 