﻿using COM.IFP.SqlSugarN;
using ORM.IFP.www.DbModel.SM;
using SqlSugar;
using System;
using System.Collections.Generic;

namespace ORM.IFP.DbModel
{
    /// <summary>
    /// 调度任务
    /// </summary>
    [SugarTable("IFP_SM_JOBINFO")]
    public partial class IFP_SM_JOBINFO
    {

        [SugarColumn(ColumnName = "GID", IsPrimaryKey = true, SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "nvarchar(36)")]
        public Field<string> Gid { get; set; }
        /// <summary>
        /// 任务名称
        /// </summary>
        [SugarColumn(ColumnName = "JOBNAME", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "NVARCHAR(255)")]
        public Field<string> JobName { get; set; }

        /// <summary>
        /// 任务描述
        /// </summary>
        [SugarColumn(ColumnName = "JOBDESC", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = true, ColumnDataType = "NVARCHAR(255)")]
        public Field<string> JobDesc { get; set; }

        /// <summary>
        /// DLL路径
        /// </summary>
        [SugarColumn(ColumnName = "DLLPATH", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "NVARCHAR(255)")]
        public Field<string> DLLPath { get; set; }

        /// <summary>
        /// 类方法路径
        /// </summary>
        [SugarColumn(ColumnName = "CLASSPATH", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "NVARCHAR(255)")]
        public Field<string> ClassPath { get; set; }

        /// <summary>
        /// 方法名称
        /// </summary>
        [SugarColumn(ColumnName = "METHODNAME", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "NVARCHAR(255)")]
        public Field<string> MethodName { get; set; }

        /// <summary>
        /// corn表达式
        /// </summary>
        [SugarColumn(ColumnName = "CORN", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = true, ColumnDataType = "NVARCHAR(255)")]
        public Field<string> Corn { get; set; }

        /// <summary>
        /// 执行方式,0：定时执行；1：循环执行
        /// </summary>
        [SugarColumn(ColumnName = "ZXFS", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "INT")]
        public Field<int> Zxfs { get; set; }

        /// <summary>
        /// 循环执行秒数
        /// </summary>
        [SugarColumn(ColumnName = "INTERVAL", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = true, ColumnDataType = "BIGINT")]
        public Field<long> Interval { get; set; }

        /// <summary>
        /// 任务状态,0:已暂停；1：已启动；
        /// </summary>
        [SugarColumn(ColumnName = "STATUS", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "INT")]
        public Field<int> Status { get; set; }

        /// <summary>
        /// 0/null 初始化、1系统添加
        /// </summary>
        [SugarColumn(ColumnName = "CREATETYPE", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = true, ColumnDataType = "INT")]
        public Field<int> CreateType { get; set; }



        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(ColumnName = "CREATOR", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = true, ColumnDataType = "NVARCHAR(128)")]
        public Field<string> Creator { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(ColumnName = "CREATETIME", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "TIMESTAMP")]
        public Field<DateTime> CreateTime { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(ColumnName = "REMARK", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = true, ColumnDataType = "NVARCHAR(3000)")]
        public Field<string> Remark { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(ColumnName = "DELT", SqlParameterDbType = typeof(FieldTypeConverter), IsNullable = false, ColumnDataType = "INT")]
        public Field<int> Delt { get; set; }

        /// <summary>
        /// 参数列表
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<IFP_SM_JOBPARAM> paramlist { set; get; }
    }
}
