using COM.IFP.Common;
using COM.IFP.SqlSugarN;
using DAL.IFP.Cookie;
using ORM.SPT.www;
using SqlSugar;
using PageModel = COM.IFP.Common.PageModel;

namespace DAL.SPT.DataQuery
{
    public class TaskInfo
    {
        // 默认设备编码
        private const string DefaultMachine_Code = "SPT1";
        // 完成状态
        private const int CompletedStatusThreshold = 2;

        #region CURD
        /// <summary>
        /// 查询任务列表
        /// </summary>
        public object TaskInfoList( IList<SPT_COMMAND> obj, PageModel? paging, bool Unfinished)
        {
            using var db = DB.Create();

            // 区分主页查询 还是 历史查询 主页只返回状态未完成的任务 反之均返回
            var query = db.Queryable<SPT_COMMAND>()
                          .WhereIF(Unfinished, x => x.STATUS < CompletedStatusThreshold)
                          .Query(obj.ToArray());
            return paging == null ? query.ToList() : query.Fetch(paging);
        }


        /// <summary>
        /// 保存任务
        /// </summary>
        public PFActionResult InsertTaskInfo(IList<SPT_COMMAND> obj)
        {
            //判定任务数据是否为空
            var validationResult = ValidateTaskCollection(obj, "任务数据不能为空");
            if (!validationResult.success)
                return validationResult;
           
            try
            {   
                // 默认值增加 
                PrepareTasksForCreation(obj);
               
                using var db = DB.Create();
                var result = db.Insertable<SPT_COMMAND>(obj).ExecuteCommand();
                return CreateActionResult(result > 0, result > 0 ? "增加成功" : "增加失败");
               
            }
            catch (Exception ex)
            {
                return new PFActionResult(false, $"{ex.Message}", null);
            }
        }



        /// <summary>
        /// 更新任务
        /// </summary>
        public PFActionResult UpdateTaskInfo(IList<SPT_COMMAND> obj)
        {
            var validationResult = ValidateTaskCollection(obj, "任务数据不能为空");
            if (!validationResult.success)
                return validationResult;

            try
            {
                using var db = DB.Create();
                var result = db.Updateable<SPT_COMMAND>()
                    .Where(x => x.GID == obj[0].GID)
                    .SetColumns(x => x.STATUS, obj[0].STATUS)
                    .ExecuteCommand();
                return CreateActionResult(result > 0, result > 0 ? "更新成功" : "更新失败");
            }
            catch (Exception ex)
            {
                return new PFActionResult(false, $"{ex.Message}", null);
            }
        }

        /// <summary>
        /// 删除任务列表
        /// </summary>
        public PFActionResult DeleteTaskInfo(IList<SPT_COMMAND> obj)
        {
            var validationResult = ValidateTaskCollection(obj, "任务数据不能为空");
            if (!validationResult.success)
                return validationResult;

            try
            {
                using var db = DB.Create();
                var result = db.Deleteable<SPT_COMMAND>(obj).ExecuteCommand();
                return CreateActionResult(result > 0, result > 0 ? "删除成功" : "删除失败");
            }
            catch (Exception ex)
            {
                return new PFActionResult(false, $"{ex.Message}", null);
            }
        }
        #endregion

        #region 辅助方法

        /// <summary>
        /// 任务集合不为空且不为空白
        /// </summary>
        private static PFActionResult ValidateTaskCollection(IList<SPT_COMMAND> tasks, string errorMessage)
        {
            if (tasks == null || !tasks.Any())
            {
                return new PFActionResult(false, errorMessage, null);
            }
            return new PFActionResult(true, string.Empty, null);
        }
        
        /// <summary>
        /// 返回
        /// </summary>
        private static PFActionResult CreateActionResult(bool success, string message)
        {
            return new PFActionResult(success, message, null);
        }

        /// <summary>
        /// 创建任务信息
        /// </summary>
        private static void PrepareTasksForCreation(IList<SPT_COMMAND> tasks)
        {
            var currentUserId = UserCache.GetUserID();

            foreach (var task in tasks)
            {
                task.MACHINE_CODE = DefaultMachine_Code;
                task.CREATOR = currentUserId;
                task.GID = Guid.NewGuid().ToString();
            }
        }

        #endregion
    }
}
