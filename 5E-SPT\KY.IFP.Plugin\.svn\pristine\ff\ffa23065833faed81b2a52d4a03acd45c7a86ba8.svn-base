﻿using COM.IFP.Common;
using COM.IFP.Log;
using COM.IFP.SqlSugarN;
using ORM.IFP.www.DbModel.UM;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DAL.IFP.Rights
{
    public class Menu
    {

        public object DelMenuByGid(string gid)
        {
            int cnt = 0;
            using (SqlSugarClient db = COM.IFP.SqlSugarN.DB.Create())
            {
                try
                {
                    cnt = db.Deleteable<IFP_UM_MENU>(new IFP_UM_MENU { Gid = gid }).ExecuteCommand();
                }
                catch (Exception ex)
                {
                    return new { success = false, msg = ex.Message };
                }
            }
            return new { success = cnt > 0, msg = $"删除数据{cnt}条" };
        }
        public object SaveMenu(List<IFP_UM_MENU> list)
        {
            int cnt = 0;

            using (SqlSugarClient db = DB.Create())
            {
                BaseDbHelper<IFP_UM_MENU> dbHelper = new BaseDbHelper<IFP_UM_MENU>(db);

                try
                {
                    foreach (IFP_UM_MENU one in list)
                    {
                        if (one.Delt == null)
                        {
                            one.Delt = 0;
                        }
                        if (one.CreateTime == null)
                        {
                            one.CreateTime = DateTime.Now;
                        }
                        if (db.Queryable<IFP_UM_MENU>().Where(a => a.Gid == one.Gid).Count() > 0)
                        {
                            cnt += db.Updateable<IFP_UM_MENU>(one).ExecuteCommand();
                        }
                        else
                        {
                            cnt += db.Insertable<IFP_UM_MENU>(one).ExecuteCommand();
                        }
                    }
                }
                catch (Exception ex)
                {
                    return new { success = false, msg = ex.Message };
                }
            }

            return new { success = list.Count == cnt, msg = $"保存数据{cnt}条" };
        }

        public object MenuList(IList<IFP_UM_MENU> filter, COM.IFP.Common.PageModel paging)
        {
            using (SqlSugarClient db = DB.Create())
            {
                var q = db.Queryable<IFP_UM_MENU>().Query(filter.ToArray()).Select(x => x);

                if (paging == null)
                {
                    var pageStruct = q.ToList();

                    return pageStruct;
                }
                else
                {
                    var res = q.Fetch(paging);
                    return res;
                }

            }
        }

        private List<IFP_UM_MENU> MenuListByRoleId(string[] roleids)
        {
            using (var db = DB.Create())
            {
                var q = from x in db.Queryable<IFP_UM_ROLE_MENU>()
                        where roleids.Contains((string)x.RoleGuid)
                        select x;

                var role_menuList = q.ToList();

                var q1 = from x in role_menuList.GroupBy(x => x.MenuGuid.Value)
                         select x.Key;  //拿到所有的MenuGuid   且去重
                var menuGidList = q1.ToList();

                var q2 = from x in db.Queryable<IFP_UM_MENU>()
                         where menuGidList.Contains((string)x.Gid) && x.Delt != 1
                         select x;
                var menuList = q2.OrderBy(x => x.Sort).ToList();
                return menuList;
            }
        }

        public List<IFP_UM_MENU> MyMenuList()
        {
            if (UserCache.LoginUserHasSuperRole())// 超级用户查看所有菜单
            {
                List<IFP_UM_MENU> param = new List<IFP_UM_MENU>();
                param.Add(new IFP_UM_MENU());
                param[0].Delt = 0;
                var res = (List<IFP_UM_MENU>)MenuList(param, null);
                return res;
            }
            if (String.IsNullOrEmpty(UserCache.GetRoleIDs()))
            {
                return null;
            }
            string[] roleids = UserCache.GetRoleIDs().Split(',');
            return MenuListByRoleId(roleids);
        }

        /// <summary>
        /// 查询对外提供的页面
        /// </summary>
        /// <returns></returns>
        public List<IFP_UM_MENU> queryOpenMenu()
        {
            using (SqlSugarClient db = DB.Create())
            {
                //这里还没定义字段
                string sql = "SELECT * FROM IFP_UM_MENU WHERE MENUURL IS NOT NULL AND MENUURL <> '' AND MENUTYPE IN (1,2)";
                List<IFP_UM_MENU> pageStruct = db.SqlQueryable<IFP_UM_MENU>(sql).ToList();
                return pageStruct;
            }
        }

        /// <summary>
        /// 查询所有的子页面用来给角色选择 不包括一二级菜单
        /// </summary>
        /// <returns></returns>
        public List<IFP_UM_MENU> QueryPageList()
        {
            using (SqlSugarClient db = DB.Create())
            {
                // 获取所有父页面ID
                var parentIds = db.Queryable<IFP_UM_MENU>()
                    .GroupBy(x => x.Pid)
                    .Select(x => x.Pid)
                    .ToList();

                // 获取所有不是父节点且父ID不为"DI"的菜单
                var leafMenus = db.Queryable<IFP_UM_MENU>()
                    .Where(x => !parentIds.Contains(x.Gid) && x.Pid != "DI")
                    .ToList();

                // 获取所有父节点信息，用于设置父页面名称
                var parentMenus = db.Queryable<IFP_UM_MENU>()
                    .Where(x => parentIds.Contains(x.Gid))
                    .ToList();

                // 创建父节点ID到名称的映射字典
                Dictionary<string, string> parentNameMap = parentMenus.ToDictionary(
                    x => x.Gid.Value,
                    x => x.MenuName.Value
                );

                // 设置每个叶子节点的父节点名称
                foreach (var menu in leafMenus)
                {
                    menu.PName = parentNameMap[menu.Pid.Value];
                }

                return leafMenus;
            }
        }

        /// <summary>
        /// 查询当前角色拥有的页面  超级管理员不使用此方法
        /// </summary>
        /// <returns></returns>
        public List<IFP_UM_MENU> QueryRolePageList(IFP_UM_ROLE obj)
        {
            using (SqlSugarClient db = DB.Create())
            {
                // 1. 获取当前角色的所有菜单GUID
                var roleMenuGuids = db.Queryable<IFP_UM_ROLE_MENU>()
                    .Where(x => x.RoleGuid == obj.Gid)
                    .Select(x => x.MenuGuid)
                    .ToList();

                // 2. 获取所有父页面ID（在pid字段中出现过的ID）
                var parentMenuIds = db.Queryable<IFP_UM_MENU>()
                    .GroupBy(x => x.Pid)
                    .Select(x => x.Pid)
                    .ToList();

                // 3. 获取所有叶子节点的GUID（不是父节点且父ID不为"DI"的菜单）
                var leafMenuGuids = db.Queryable<IFP_UM_MENU>()
                    .Where(x => !parentMenuIds.Contains(x.Gid) && x.Pid != "DI")
                    .Select(x => x.Gid)
                    .ToList();

                // 4. 获取当前角色有权限的叶子节点菜单
                var roleLeafMenus = db.Queryable<IFP_UM_MENU>()
                    .Where(x => roleMenuGuids.Contains(x.Gid) && leafMenuGuids.Contains(x.Gid))
                    .ToList();

                // 5. 获取所有父节点信息用于名称映射
                var parentMenus = db.Queryable<IFP_UM_MENU>()
                    .Where(x => parentMenuIds.Contains(x.Gid))
                    .ToList();

                // 6. 创建父节点ID到名称的映射字典
                Dictionary<string, string> parentNameMap = parentMenus.ToDictionary(
                    key => key.Gid.Value,
                    value => value.MenuName.Value
                );

                // 7. 设置每个菜单的父节点名称
                foreach (var menu in roleLeafMenus)
                {
                    menu.PName = parentNameMap[menu.Pid.Value];
                }

                return roleLeafMenus;
            }

        }

        /// <summary>
        /// 保存当前角色拥有的页面  超级管理员不使用此方法
        /// </summary>
        /// <returns></returns>
        public PFActionResult SaveRolePageList(IFP_UM_ROLE_MENU obj)
        {
            using (SqlSugarClient db = DB.Create())
            {
                try
                {
                    // 开启事务
                    db.BeginTran();

                    // 获取所有菜单
                    var allMenus = db.Queryable<IFP_UM_MENU>().ToList();

                    // 创建Gid到菜单实体的映射和标记字典
                    Dictionary<string, IFP_UM_MENU> menuMap = allMenus.ToDictionary(
                        key => key.Gid.Value,
                        value => value
                    );

                    Dictionary<string, bool> menuFlags = allMenus.ToDictionary(
                        key => key.Gid.Value,
                        value => false
                    );

                    // 处理需要添加的菜单及其父节点
                    foreach (var menuGid in obj.MenuGidList)
                    {
                        menuFlags[menuGid] = true;  // 标记当前菜单

                        // 递归处理父节点
                        string parentId = menuMap[menuGid].Pid.Value;
                        while (menuFlags.ContainsKey(parentId) && !menuFlags[parentId])
                        {
                            menuFlags[parentId] = true;
                            parentId = menuMap[parentId].Pid.Value;
                        }
                    }

                    // 删除当前角色的所有菜单权限
                    db.Deleteable<IFP_UM_ROLE_MENU>()
                        .Where(x => x.RoleGuid == obj.RoleGuid)
                        .ExecuteCommand();

                    // 批量插入新的权限记录
                    var newRoleMenus = menuFlags
                        .Where(x => x.Value)
                        .Select(x => new IFP_UM_ROLE_MENU
                        {
                            Gid = Guid.NewGuid().ToString("N"),
                            RoleGuid = obj.RoleGuid,
                            MenuGuid = x.Key,
                            Delt = 0,
                            CreateTime = DateTime.Now
                        })
                        .ToList();

                    // 使用SqlSugar的批量插入
                    db.Insertable(newRoleMenus).ExecuteCommand();

                    // 提交事务
                    db.CommitTran();

                    return new PFActionResult
                    {
                        success = true,
                        msg = "修改完成"
                    };
                }
                catch (Exception ex)
                {
                    // 回滚事务
                    db.RollbackTran();
                    return new PFActionResult
                    {
                        success = false,
                        msg = "修改失败：" + ex.Message
                    };
                }
            }
        }

        /// <summary>
        /// 角色可用和不可用菜单分别列举
        /// </summary>
        /// <param name="roleids"></param>
        /// <returns></returns>
        public PFActionResult AvailableMenu(string[] roleids)
        {
            PFActionResult res = new PFActionResult();
            res.success = false;
            try
            {
                List<IFP_UM_MENU> param = new List<IFP_UM_MENU>();
                param.Add(new IFP_UM_MENU() { Delt = 0 }); //必须是已启用的菜单
                var allMenu = (List<IFP_UM_MENU>)MenuList(param, null);

                List<IFP_UM_MENU> availableMenu = null;
                if (roleids.Contains(COM.IFP.Common.StaticConfig.SuperRoleId))
                {
                    availableMenu = allMenu;
                }
                else
                {
                    availableMenu = MenuListByRoleId(roleids);
                }
                var availableMenuGid = availableMenu.Select(x => x.Gid);
                var unavailableMenu = allMenu.Where(x => !availableMenuGid.Contains(x.Gid)).Select(x => x);
                res.success = true;
                res.data = new Dictionary<string, object>()
                {
                    {"availableMenu",availableMenu },
                    {"unavailableMenu",unavailableMenu },
                };
                return res;
            }
            catch (Exception e)
            {
                res.success = false;
                res.msg = e.Message;
                return res;
            }
        }

        #region UBAC版本菜单新增接口
        /// <summary>
        /// 获取用户有效菜单列表 (UBAC版本)，同时考虑角色继承权限和用户直接权限
        /// </summary>
        /// <param name="userGid">用户ID</param>
        /// <returns>用户有效菜单列表</returns>
        public List<IFP_UM_MENU> MyMenuListUBAC(string userGid)
        {
            try
            {
                if (string.IsNullOrEmpty(userGid))
                {
                    LoggerHelper.Warning("用户ID为空，返回空菜单列表");
                    return new List<IFP_UM_MENU>();
                }

                // 检查是否支持UBAC功能
                if (!IsUBACSupported())
                {
                    LoggerHelper.Info("当前数据库不支持UBAC，回退到角色模式");
                    return MyMenuList();
                }

                // 检查是否为超级管理员
                if (IsUserSuperAdmin(userGid))
                {
                    LoggerHelper.Info($"用户 {userGid} 是超级管理员，返回所有菜单");
                    return GetAllMenus();
                }

                // 使用UBAC服务获取用户有效权限
                var ubacService = new UBAC();
                var permissionResult = ubacService.GetUserEffectivePermissions(userGid);

                if (!permissionResult.success || permissionResult.data == null)
                {
                    LoggerHelper.Warning($"UBAC获取用户 {userGid} 权限失败: {permissionResult.msg}");
                    return new List<IFP_UM_MENU>();
                }

                // 从UBAC结果中提取有效菜单GID列表
                List<string> effectiveMenuGids = ExtractEffectiveMenuGids(permissionResult.data);

                if (effectiveMenuGids.Count == 0)
                {
                    LoggerHelper.Info($"用户 {userGid} 没有有效的菜单权限");
                    return new List<IFP_UM_MENU>();
                }

                LoggerHelper.Info($"用户 {userGid} 有效菜单权限: {string.Join(",", effectiveMenuGids)}");

                // 根据菜单GID获取完整菜单信息
                var menus = GetMenusByGids(effectiveMenuGids);

                LoggerHelper.Info($"最终返回菜单数量: {menus.Count}");
                if (menus.Count > 0)
                {
                    LoggerHelper.Info($"菜单列表: {string.Join(", ", menus.Select(m => $"{m.Gid.Value}-{m.MenuName.Value}"))}");
                }

                return menus;
            }
            catch (Exception ex)
            {
                LoggerHelper.Error(ErrorList.E9999, $"获取用户UBAC菜单失败: {ex.Message}", ex);
                return new List<IFP_UM_MENU>();
            }
        }

        /// <summary>
        /// 检查是否支持UBAC功能（检查相关表是否存在）
        /// </summary>
        /// <returns>是否支持UBAC</returns>
        public bool IsUBACSupported()
        {
            try
            {
                using (SqlSugarClient db = DB.Create())
                {
                    // 检查UBAC相关表是否存在
                    var tableNames = new[] { "IFP_UM_USER_MENU", "IFP_UM_USER_BTN" };

                    foreach (var tableName in tableNames)
                    {
                        var exists = db.DbMaintenance.IsAnyTable(tableName);
                        if (!exists)
                        {
                            LoggerHelper.Warning($"UBAC表 {tableName} 不存在");
                            return false;
                        }
                    }

                    return true;
                }
            }
            catch (Exception ex)
            {
                LoggerHelper.Error(ErrorList.E9999, $"检查UBAC支持失败: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 检查用户是否为超级管理员
        /// </summary>
        /// <param name="userGid">用户ID</param>
        /// <returns>是否为超级管理员</returns>
        private bool IsUserSuperAdmin(string userGid)
        {
            try
            {
                using (SqlSugarClient db = DB.Create())
                {
                    var superRoleId = COM.IFP.Common.StaticConfig.SuperRoleId;

                    var hasSuperRole = db.Queryable<IFP_UM_USER_ROLE>()
                        .Where(x => x.UsiGuid == userGid && x.RoleGuid == superRoleId && x.Delt == 0)
                        .Any();

                    return hasSuperRole;
                }
            }
            catch (Exception ex)
            {
                LoggerHelper.Error(ErrorList.E9999, $"检查超级管理员权限失败: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 获取所有菜单（用于超级管理员）
        /// </summary>
        /// <returns>所有菜单列表</returns>
        private List<IFP_UM_MENU> GetAllMenus()
        {
            try
            {
                using (SqlSugarClient db = DB.Create())
                {
                    var menus = db.Queryable<IFP_UM_MENU>()
                        .Where(x => x.Delt == 0)
                        .OrderBy(x => x.Sort)
                        .ToList();

                    return menus;
                }
            }
            catch (Exception ex)
            {
                LoggerHelper.Error(ErrorList.E9999, $"获取所有菜单失败: {ex.Message}", ex);
                return new List<IFP_UM_MENU>();
            }
        }

        /// <summary>
        /// 根据菜单GID列表获取菜单详细信息
        /// </summary>
        /// <param name="menuGids">菜单GID列表</param>
        /// <returns>菜单列表</returns>
        private List<IFP_UM_MENU> GetMenusByGids(List<string> menuGids)
        {
            try
            {
                using (SqlSugarClient db = DB.Create())
                {
                    var menus = db.Queryable<IFP_UM_MENU>()
                        .Where(x => menuGids.Contains((string)x.Gid) && x.Delt == 0)
                        .OrderBy(x => x.Sort)
                        .ToList();

                    return menus;
                }
            }
            catch (Exception ex)
            {
                LoggerHelper.Error(ErrorList.E9999, $"根据GID获取菜单失败: {ex.Message}", ex);
                return new List<IFP_UM_MENU>();
            }
        }

        /// <summary>
        /// 从UBAC权限结果中提取有效菜单GID列表
        /// </summary>
        /// <param name="ubacData">UBAC权限数据</param>
        /// <returns>菜单GID列表</returns>
        private List<string> ExtractEffectiveMenuGids(object ubacData)
        {
            var effectiveMenuGids = new List<string>();

            try
            {
                // 将动态对象转换为可处理的格式
                string jsonString = Newtonsoft.Json.JsonConvert.SerializeObject(ubacData);
                dynamic data = Newtonsoft.Json.JsonConvert.DeserializeObject(jsonString);

                LoggerHelper.Info($"UBAC原始数据: {jsonString}");

                // 优先使用 EffectiveMenus
                if (data?.MenuPermissions?.EffectiveMenus != null)
                {
                    LoggerHelper.Info("使用 EffectiveMenus 提取菜单权限");
                    foreach (var menuGid in data.MenuPermissions.EffectiveMenus)
                    {
                        if (menuGid != null)
                        {
                            effectiveMenuGids.Add(menuGid.ToString());
                        }
                    }
                }
                // 备用：从 MenuDetails 中提取
                else if (data?.MenuPermissions?.MenuDetails != null)
                {
                    LoggerHelper.Info("使用 MenuDetails 提取菜单权限");
                    foreach (var menuDetail in data.MenuPermissions.MenuDetails)
                    {
                        if (menuDetail?.MenuGid != null)
                        {
                            effectiveMenuGids.Add(menuDetail.MenuGid.ToString());
                        }
                    }
                }
                // 兜底：从 TotalMenus 中提取
                else if (data?.MenuPermissions?.TotalMenus != null)
                {
                    LoggerHelper.Info("使用 TotalMenus 提取菜单权限");
                    foreach (var menuGid in data.MenuPermissions.TotalMenus)
                    {
                        if (menuGid != null)
                        {
                            effectiveMenuGids.Add(menuGid.ToString());
                        }
                    }
                }
                else
                {
                    LoggerHelper.Warning("UBAC数据中未找到菜单权限信息");
                }

                LoggerHelper.Info($"提取到的菜单GID: {string.Join(",", effectiveMenuGids)}");
            }
            catch (Exception ex)
            {
                LoggerHelper.Error(ErrorList.E9999, $"解析UBAC菜单权限数据失败: {ex.Message}", ex);
            }

            return effectiveMenuGids.Distinct().ToList();
        }
        #endregion
    }
}
