﻿using COM.IFP.Common;
using COM.IFP.PLC.Modbus;
using COM.IFP.PLC.SiemensS7;
using DAL.SPT.Service.CardReaders;
using DAL.SPT.Service.Diagram;
using DAL.SPT.Service.PipelineProgress;
using DAL.SPT.Service.TokenExecute;
using DAL.SPT.www;
using ORM.IFP.DbModel;
using ORM.SPT;
using ORM.SPT.www;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DAL.SPT.Service
{
    public static class Global
    {
        #region
        /// <summary>
        /// 后台运行状态,true为运行，否则停止
        /// </summary>
        public static bool RUN_STATUS { set; get; } = false;

        /// <summary>
        /// 轮询间隔没超过最大间隔判定PLC连通
        /// </summary>
        public static bool NO_OVER_TIME { set; get; } = true;

        /// <summary>
        /// PLC的连接状态存储在数据集中，需要单独拿出
        /// </summary>
        static public bool PlcIsConnected
        {
            get
            {
                bool b;
                if (ModbusAdaptor == null) { b = false; }
                else
                {
                    b = ModbusAdaptor.GetStatus();//todo
                }
                //如果socket连通且轮询间隔没超，则判定连通
                return (b && NO_OVER_TIME);
            }
        }

        /// <summary>
        /// 轮询点位的延迟时间
        /// </summary>
        public static double PointReadDelay { set; get; } = -1;

        /// <summary>
        /// 轮询运行状态,true为运行，否则停止
        /// </summary>
        public static bool ECHO_POINT_STATUS { set; get; } = false;

        /// <summary>
        /// 读卡器服务运行状态
        /// </summary>
        public static bool CARD_READER_RUNNING { set; get; } = false;

        /// <summary>
        /// 读卡器暂停状态
        /// </summary>
        public static bool CARD_READER_PAUSED { set; get; } = false;

        /// <summary>
        /// 任务执行器服务运行状态
        /// </summary>
        public static bool TASK_EXECUTOR_RUNNING { set; get; } = false;

        /// <summary>
        /// 传瓶进度服务运行状态
        /// </summary>
        public static bool PIPELINE_PROGRESS_RUNNING { set; get; } = false;

        /// <summary>
        /// 令牌服务运行状态
        /// </summary>
        public static bool TOKEN_SERVICE_RUNNING { set; get; } = false;
        /// <summary>
        /// 轮询最小时间间隔单位ms
        /// </summary>
        public static int MIN_INTERVALS = 200;
        /// <summary>
        /// 轮询大时间间隔单位ms
        /// </summary>
        public static int MAX_INTERVALS = 4000;

        /// <summary>
        /// 检测连接状态的时间间隔
        /// </summary>
        public static int CHECK_INTERVALS = 5000;
        #endregion

        /// <summary>
        /// 当前传瓶进度数据
        /// </summary>
        public static List<TaskStationData> CurrentPipelineProgressData { get; set; }
        /// <summary>
        /// 未完成任务列表数据
        /// </summary>
        public static List<Dictionary<string, object>> UnfinishedTaskListDatas { get; set; }
        /// <summary>
        /// 报警列表数据
        /// </summary>
        public static List<Dictionary<string, object>> AlarmListData { get; set; }
        /// <summary>
        /// 运行信息列表数据
        /// </summary>
        public static List<SPT_PROCESS_INFO> RunInfoListData { get; set; }

        public static List<Point> AllPoint;


        public static Execute Execute { set; get; }
        public static ServiceCPU CPU { set; get; }

        /// <summary>
        /// 读卡器服务实例
        /// </summary>
        public static CardReader CardReaderService { set; get; }

        /// <summary>
        /// 任务执行器服务实例
        /// </summary>
        public static TaskExecutor TaskExecutorService { set; get; }

        /// <summary>
        /// 传瓶进度服务实例
        /// </summary>
        public static PipelineProgressService PipelineProgressService { set; get; }

        /// <summary>
        /// 令牌服务实例
        /// </summary>
        public static TokenService TokenService { set; get; }
        
        /// <summary>
        /// 适配器
        /// </summary>
        public static ModbusTCPAdaptor ModbusAdaptor;

        /// <summary>
        /// 通讯适配器
        /// </summary>
        //public static Adaptor ADA { set; get; }

        //public static S7Adaptor S7Client { set; get; }
        public static DataStorage DataMemory { set; get; }
        public static Lazy<MainDAL> DB_Option { get; } = Entity.Create<MainDAL>();

        //public static Node Root { set; get; }

        #region 通用点位操作方法

        /// <summary>
        /// 检查点位值
        /// </summary>
        /// <param name="pointName">点位名称</param>
        /// <param name="expectedValue">期望值</param>
        /// <returns>是否匹配期望值</returns>
        public static bool CheckPointValue(string pointName, object expectedValue)
        {
            try
            {
                if (DataMemory?.Data?.TryGetValue(pointName, out var point) == true)
                {
                    return Equals(point.Value, expectedValue);
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 写入点位值
        /// </summary>
        /// <param name="pointName">点位名称</param>
        /// <param name="value">要写入的值</param>
        /// <returns>是否写入成功</returns>
        public static bool WritePointValue(string pointName, object value)
        {
            try
            {
                if (DataMemory?.Data?.TryGetValue(pointName, out var point) == true)
                {
                    DataMemory.Write(point, value);
                    return true;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }
        #endregion
    }
}
