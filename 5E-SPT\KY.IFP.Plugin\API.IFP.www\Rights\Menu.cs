﻿using COM.IFP.Common;
using COM.IFP.Log;
using DAL.IFP.Cookie;
using Newtonsoft.Json;
using ORM.IFP.www.DbModel.UM;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;

namespace API.IFP.Rights
{
    public class Menu
    {
        private Lazy<DAL.IFP.Rights.Menu> _service = Entity.Create<DAL.IFP.Rights.Menu>();

        public List<IFP_UM_MENU> MyMenuList(JsonElement json)
        {
            List<IFP_UM_MENU> reList = _service.Value.MyMenuList();
            if (reList == null)
            {
                return null;
            }
            return reList;
        }

        public object MenuList(JsonElement json)
        {
            IList<IFP_UM_MENU> filter = new List<IFP_UM_MENU>();
            var tmp1 = new JsonElement();
            if (json.TryGetProperty("filter", out tmp1) == true)
            {
                filter = json.GetValue<IList<IFP_UM_MENU>>("filter");
            }

            PageModel paging = null;
            var tmp = new JsonElement();
            if (json.TryGetProperty("paging", out tmp) == true)
            {
                paging = json.GetValue<PageModel>("paging");
            }

            return _service.Value.MenuList(filter, paging);
        }

        public object DelMenuByGid(JsonElement json)
        {
            string gid = json.GetValue<string>("gid");
            return _service.Value.DelMenuByGid(gid);
        }
        public object SaveMenu(JsonElement json)
        {
            // 创建序列化设置并添加Newtonsoft的JSON转换器
            var settings = new JsonSerializerSettings
            {
                Converters = new List<JsonConverter> { new NewtonsoftFieldConverter() }
            };

            // 使用自定义设置进行反序列化
            List<IFP_UM_MENU> list = JsonConvert.DeserializeObject<List<IFP_UM_MENU>>(json.ToString(), settings);
            return _service.Value.SaveMenu(list);
        }

        /// <summary>
        /// 查询所有的子页面用来给角色选择 不包括一二级菜单
        /// </summary>
        /// <returns></returns>
        public List<IFP_UM_MENU> QueryPageList(JsonElement json)
        {
            return _service.Value.QueryPageList();
        }
        /// <summary>
        /// 查询当前角色拥有的页面  超级管理员不使用此方法
        /// </summary>
        /// <returns></returns>
        public List<IFP_UM_MENU> QueryRolePageList(JsonElement json)
        {
            // 创建序列化设置并添加Newtonsoft的JSON转换器
            var settings = new JsonSerializerSettings
            {
                Converters = new List<JsonConverter> { new NewtonsoftFieldConverter() }
            };

            // 使用自定义设置进行反序列化
            IFP_UM_ROLE entity = JsonConvert.DeserializeObject<IFP_UM_ROLE>(json.ToString(), settings);
            return _service.Value.QueryRolePageList(entity);
        }
        /// <summary>
        /// 保存当前角色拥有的页面  超级管理员不使用此方法
        /// </summary>
        /// <returns></returns>
        public PFActionResult SaveRolePageList(JsonElement json)
        {
            // 创建序列化设置并添加Newtonsoft的JSON转换器
            var settings = new JsonSerializerSettings
            {
                Converters = new List<JsonConverter> { new NewtonsoftFieldConverter() }
            };
            IFP_UM_ROLE_MENU entity = JsonConvert.DeserializeObject<IFP_UM_ROLE_MENU>(json.ToString(), settings);

            return _service.Value.SaveRolePageList(entity);
        }

        /// <summary>
        /// daiabin角色可用和不可用菜单分别列举
        /// </summary>
        /// <param name="json"></param>
        /// <returns></returns>
        public PFActionResult AvailableMenu(JsonElement json)
        {
            try
            {
                string[] roleids = json.GetProperty("roleIDs").GetString().Split(",");
                return _service.Value.AvailableMenu(roleids);
            }
            catch (Exception e)
            {
                return new PFActionResult()
                {
                    success = false,
                    msg = $"json解析错误;{e.Message}"
                };
            }

        }

        #region UBAC版本菜单新增接口
        /// <summary>
        /// 获取用户有效菜单列表（UBAC版本）
        /// 同时考虑角色继承权限和用户直接权限，保持与老项目的兼容性
        /// </summary>
        /// <param name="json">请求参数（可为空）</param>
        /// <returns>用户有效菜单列表</returns>
        public List<IFP_UM_MENU> MyMenuListUBAC(JsonElement json)
        {
            try
            {
                // 获取当前登录用户ID
                string currentUserGid = UserCache.GetUserID();
                if (string.IsNullOrEmpty(currentUserGid))
                {
                    LoggerHelper.Warning("MyMenuListUBAC: 当前用户ID为空");
                    return new List<IFP_UM_MENU>();
                }

                LoggerHelper.Info($"MyMenuListUBAC: 开始获取用户 {currentUserGid} 的菜单权限");

                // 调用DAL层方法
                var result = _service.Value.MyMenuListUBAC(currentUserGid);

                LoggerHelper.Info($"MyMenuListUBAC: 返回菜单数量 {result?.Count ?? 0}");

                return result ?? new List<IFP_UM_MENU>();
            }
            catch (Exception ex)
            {
                LoggerHelper.Error(ErrorList.E9999, $"MyMenuListUBAC接口调用失败: {ex.Message}", ex);

                // 发生异常时，尝试降级到原有接口
                try
                {
                    LoggerHelper.Info("尝试降级到原有MyMenuList接口");
                    return _service.Value.MyMenuList() ?? new List<IFP_UM_MENU>();
                }
                catch (Exception fallbackEx)
                {
                    LoggerHelper.Error(ErrorList.E9999, $"降级到原有接口也失败: {fallbackEx.Message}", fallbackEx);
                    return new List<IFP_UM_MENU>();
                }
            }
        }

        /// <summary>
        /// 获取用户有效菜单列表的详细信息（UBAC调试版本）
        /// 提供更详细的调试信息，用于问题排查
        /// </summary>
        /// <param name="json">请求参数</param>
        /// <returns>包含详细信息的响应</returns>
        public object MyMenuListUBACDebug(JsonElement json)
        {
            try
            {
                string currentUserGid = UserCache.GetUserID();
                string currentRoleIds = UserCache.GetRoleIDs();

                LoggerHelper.Info($"MyMenuListUBACDebug: 用户 {currentUserGid}, 角色 {currentRoleIds}");

                var debugInfo = new
                {
                    UserGid = currentUserGid,
                    RoleIds = currentRoleIds,
                    Timestamp = DateTime.Now,
                    Step = 1,
                    Message = "开始获取菜单"
                };

                if (string.IsNullOrEmpty(currentUserGid))
                {
                    return new
                    {
                        success = false,
                        msg = "当前用户ID为空",
                        debug = debugInfo,
                        data = new List<IFP_UM_MENU>()
                    };
                }

                // 调用DAL层方法
                var menuList = _service.Value.MyMenuListUBAC(currentUserGid);

                // 构建详细的调试信息
                var detailedDebugInfo = new
                {
                    UserGid = currentUserGid,
                    RoleIds = currentRoleIds,
                    Timestamp = DateTime.Now,
                    MenuCount = menuList?.Count ?? 0,
                    MenuSummary = menuList?.Select(m => new
                    {
                        Gid = m.Gid.Value,
                        Name = m.MenuName.Value,
                        Url = m.MenuUrl.Value,
                        Sort = m.Sort.Value
                    }).ToList(),
                    IsUBACSupported = _service.Value.IsUBACSupported(), // 需要在DAL中公开这个方法
                    IsSuperAdmin = UserCache.LoginUserHasSuperRole()
                };

                return new
                {
                    success = true,
                    msg = $"获取菜单成功，共 {menuList?.Count ?? 0} 个",
                    debug = detailedDebugInfo,
                    data = menuList ?? new List<IFP_UM_MENU>()
                };
            }
            catch (Exception ex)
            {
                LoggerHelper.Error(ErrorList.E9999, $"MyMenuListUBACDebug失败: {ex.Message}", ex);

                return new
                {
                    success = false,
                    msg = $"调试接口异常: {ex.Message}",
                    debug = new
                    {
                        UserGid = UserCache.GetUserID(),
                        Timestamp = DateTime.Now,
                        Error = ex.Message,
                        StackTrace = ex.StackTrace
                    },
                    data = new List<IFP_UM_MENU>()
                };
            }
        }

        /// <summary>
        /// 比较UBAC版本和原版本的菜单差异（用于调试和验证）
        /// </summary>
        /// <param name="json">请求参数</param>
        /// <returns>菜单对比结果</returns>
        public object CompareMenuListVersions(JsonElement json)
        {
            try
            {
                string currentUserGid = UserCache.GetUserID();

                // 获取原版本菜单
                var originalMenus = _service.Value.MyMenuList() ?? new List<IFP_UM_MENU>();

                // 获取UBAC版本菜单
                var ubacMenus = _service.Value.MyMenuListUBAC(currentUserGid) ?? new List<IFP_UM_MENU>();

                // 提取菜单GID进行比较
                var originalGids = originalMenus.Select(m => m.Gid.Value).Where(gid => !string.IsNullOrEmpty(gid)).ToHashSet();
                var ubacGids = ubacMenus.Select(m => m.Gid.Value).Where(gid => !string.IsNullOrEmpty(gid)).ToHashSet();

                // 计算差异
                var onlyInOriginal = originalGids.Except(ubacGids).ToList();
                var onlyInUBAC = ubacGids.Except(originalGids).ToList();
                var common = originalGids.Intersect(ubacGids).ToList();

                return new
                {
                    success = true,
                    msg = "菜单版本对比完成",
                    data = new
                    {
                        UserGid = currentUserGid,
                        Original = new
                        {
                            Count = originalMenus.Count,
                            MenuGids = originalGids.ToList(),
                            Menus = originalMenus.Select(m => new
                            {
                                Gid = m.Gid.Value,
                                Name = m.MenuName.Value,
                                Url = m.MenuUrl.Value
                            }).ToList()
                        },
                        UBAC = new
                        {
                            Count = ubacMenus.Count,
                            MenuGids = ubacGids.ToList(),
                            Menus = ubacMenus.Select(m => new
                            {
                                Gid = m.Gid.Value,
                                Name = m.MenuName.Value,
                                Url = m.MenuUrl.Value
                            }).ToList()
                        },
                        Comparison = new
                        {
                            CommonCount = common.Count,
                            CommonMenus = common,
                            OnlyInOriginal = onlyInOriginal,
                            OnlyInUBAC = onlyInUBAC,
                            IsSame = onlyInOriginal.Count == 0 && onlyInUBAC.Count == 0
                        }
                    }
                };
            }
            catch (Exception ex)
            {
                LoggerHelper.Error(ErrorList.E9999, $"菜单版本对比失败: {ex.Message}", ex);

                return new
                {
                    success = false,
                    msg = $"菜单版本对比异常: {ex.Message}",
                    data = (object)null
                };
            }
        }
        #endregion
    }
}
