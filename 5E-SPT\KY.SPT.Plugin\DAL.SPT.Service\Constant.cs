﻿namespace DAL.SPT.Service
{
    public class Constant
    {
        public const string FJYXZT = "风机运行状态";
        public const string ZXQWZT = "转吸气位状态";
        public const string ZCQWZT = "转吹气位状态";
        public const string XQWZZT = "吸气位置状态";
        public const string CQWZZT = "吹气位置状态";
        public const string FJQDML = "风机启动命令";
        public const string FJTZML = "风机停止命令";
        public const string ZXQWML = "转吸气位命令";
        public const string ZCQWML = "转吹气位命令";
        public const string PLWML1 = "频率位一命令";
        public const string PLWML2 = "频率位二命令";
        public const string PLWML3 = "频率位三命令";
        public const string PLSXML = "频率生效命令";

        public const string SJSS = "升降上升";
        public const string SJXJ = "升降下降";

        public const string ZGDML1 = "转管道一命令";
        public const string ZGDML2 = "转管道二命令";
        public const string ZGDML3 = "转管道三命令";
        public const string ZGDML4 = "转管道四命令";

        public const string HDFDK = "滑动阀打开";
        public const string HDFGB = "滑动阀关闭";


        public const string TGSC = "推杆伸出";
        public const string TGSH = "推杆缩回";

        public const string GDJC = "管道检测";
        public const string ZBJXXH = "准备就绪信号";

        public const string FPTCML = "发瓶推出命令";
        public const string FPSHML = "发瓶缩回命令";
        public const string SPTCML = "收瓶推出命令";
        public const string SPSHML = "收瓶缩回命令";

        public const string XJDXXW = "下降到下限位";
        public const string XJDZXW = "下降到中限位";

        public const string XPJD = "小瓶就绪";
        public const string DPJD = "大瓶就绪";
        public const string ZPJD = "中瓶就绪";
        public const string YXDKZT = "允许读卡状态";

        public const string TXZD = "通讯中断";
        public const string YPJC = "样瓶检测";

        public const string QQLPZT = "请求令牌状态";
        public const string ZDLPML = "自动令牌命令";
        public const string SYLPZT = "授予令牌状态";

        #region 系统站专有
        public const string ZDYXML = "自动运行命令";
        public const string LCFWML = "流程复位命令";
        public const string BJFWML = "报警复位命令";
        //public const string SBJXZT = "设备就绪状态";
        public const string FSZDML = "发送站点命令";
        public const string JSZDML = "接收站点命令";
        public const string CSWCZT = "传送完成状态";
        public const string XQWCZT = "吸气完成状态";
        //public const string CSBSML = "传输标识命令";
        public const string CSCSZT = "传送超时状态";
        public const string LJCWZT = "路径错误状态";
        public const string BNJSZT = "不能接收状态";
        public const string SFDPML = "是否大瓶命令";
        #endregion


    }
    public enum EnumPLC
    {
        Modbus = 0,
        Siemens = 1,
    }


    /// <summary>
    /// 站点类型
    /// </summary>
    public enum EnumStationType
    {
        /// <summary>
        /// 系统站
        /// </summary>
        System = 0,
        /// <summary>
        /// 风机站
        /// </summary>
        Fan = 1,
        /// <summary>
        /// 发送站
        /// </summary>
        Send = 2,
        /// <summary>
        /// 换向站
        /// </summary>
        Select = 3,
        /// <summary>
        /// 弃样站
        /// </summary>
        Discard = 4,
        /// <summary>
        /// 接收站
        /// </summary>
        Receive = 5,
        /// <summary>
        /// 样柜站
        /// </summary>
        ASS = 6,
        /// <summary>
        /// 人工站
        /// </summary>
        Manual = 7,
        /// <summary>
        /// 收发站
        /// </summary>
        ReceiveSend = 8,

        /// <summary>
        /// 单发站
        /// </summary>
        OnlySend = 9,
        /// <summary>
        /// 单收站
        /// </summary>
        OnlyReceive = 10,

        /// <summary>
        /// 机器人制样收发站
        /// </summary>
        RobotReceiveSend = 11

    }
}
