﻿using COM.IFP.Common;
using COM.IFP.Log;
using COM.IFP.SqlSugarN;
using Microsoft.IdentityModel.Tokens;
using ORM.IFP;
using ORM.IFP.DbModel.UM;
using ORM.IFP.DTO;
using ORM.IFP.www.DbModel.UM;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using StaticConfig = COM.IFP.Common.StaticConfig;

namespace DAL.IFP.Rights
{
    public class Role
    {
        /// <summary>
        /// 所有权限列表
        /// </summary>
        /// <returns></returns>
        public List<IFP_UM_ROLE> RoleList()
        {
            using (var db = DB.Create())
            {
                var query = from x in db.Queryable<IFP_UM_ROLE>()
                            orderby x.RoleCode
                            select x;
                List<IFP_UM_ROLE> pageStruct = query.ToList();
                return pageStruct;
            }
            //return null;
        }

        /// <summary>
        /// 本角色可用的菜单
        /// </summary>
        /// <param name="roleGid"></param>
        /// <returns></returns>
        public List<IFP_UM_MENU> RoleMenu(string roleGid)
        {
            using (var db = DB.Create())
            {
                IFP_UM_MENU jobj = new IFP_UM_MENU();
                var list1 = (from x in db.Queryable<IFP_UM_ROLE_MENU>()
                             where x.RoleGuid == roleGid
                             select x.MenuGuid).ToList();
                var list2 = (from x in db.Queryable<IFP_UM_MENU>()
                             where list1.Contains(x.Gid)
                             select x).ToList();
                return list2;
            }
        }

        /// <summary>
        /// 角色管理查询
        /// </summary>
        /// <param name="filter"></param>
        /// <param name="paging"></param>
        /// <returns></returns>
        public PageModel<IFP_UM_ROLE> SelectRole(IList<IFP_UM_ROLE> filter, COM.IFP.Common.PageModel paging)
        {
            using (var db = DB.Create())
            {
                var wheres = filter.ToArray();
                var result = db.Queryable<IFP_UM_ROLE>().Where(wheres).Order(wheres).Fetch(paging);
                //var Sql = db.Queryable<IFP_UM_MENU>().Where(filter.ToArray()).Order(wheres).ToSql();
                return result;
            }
        }

        /// <summary>
        /// 指定用户拥有的权限IDs
        /// </summary>
        /// <param name="gid">用户gid</param>
        /// <returns></returns>
        public string UserRoleList(string gid)
        {
            using (var db = DB.Create())
            {
                var roleGuids = db.Queryable<IFP_UM_ROLE>()
                    .LeftJoin<IFP_UM_USER_ROLE>((a, b) => a.Gid == b.RoleGuid)
                    .Where((a, b) => b.UsiGuid == gid)
                    .Select(a => (string)a.Gid)
                    .ToList();
                //var str = roleGuids.ToSqlString();
                return string.Join(",", roleGuids);
            }
        }

        /// <summary>
        ///  当前用户可以管理的权限列表
        /// </summary>
        /// <param name="userid"></param>
        /// <returns></returns>
        private List<IFP_UM_ROLE> RoleListByuserId(string userid)
        {
            using (var db = DB.Create())
            {
                var roleList = db.Queryable<IFP_UM_ROLE>()
                    .Where(role =>
                        SqlFunc.Subqueryable<IFP_UM_USER_ROLE>()
                            .LeftJoin<IFP_UM_USER_INFO>((a, b) => a.UsiGuid == b.Gid)
                            .Where((a, b) => b.Gid == userid)
                            .Select(a => a.RoleGuid.Value)
                            .Contains(role.Gid.Value)
                    )
                    .OrderBy(x => x.RoleCode, OrderByType.Asc)
                    .ToList();

                return roleList;
            }
        }

        /// <summary>
        /// 当前用户可以管理的权限列表
        /// </summary>
        /// <returns></returns>
        public List<IFP_UM_ROLE> MyRoleList()
        {
            if (UserCache.LoginUserHasSuperRole())// 超级用户设置所有权限
            {
                return RoleList();
            }
            else
            {
                //daiabin排除超级管理员权限返回。
                var q = from x in RoleList()
                        where x.Gid != COM.IFP.Common.StaticConfig.SuperRoleId
                        select x;
                return q.ToList();
            }

        }


        public PFActionResult UpdateUserRole(string gid, string roleIds)
        {
            PFActionResult res = new PFActionResult();
            using (var db = DB.Create())
            {
                //Dictionary<string, bool> result = new Dictionary<string, bool>();
                //清空用户的历史权限信息
                int count = db.Deleteable<IFP_UM_USER_ROLE>().Where(a => a.UsiGuid == gid).ExecuteCommand();

                if (string.IsNullOrWhiteSpace(roleIds))
                {
                    res.success = true;
                }

                string[] arrRole = roleIds.Split(",");
                foreach (string strRole in arrRole)
                {
                    db.Insertable(new IFP_UM_USER_ROLE()
                    {
                        Gid = Guid.NewGuid().ToString("N"),
                        UsiGuid = gid,
                        RoleGuid = strRole,
                        Delt = 0,
                        CreateTime = DateTime.Now,
                        Creator = UserCache.GetUserID()
                    }).ExecuteCommand();
                }
                res.success = true;
                return res;
            }

        }

        /// <summary>
        ///  判断当前角色是不是超级管理员
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public string SuperAdmin()
        {
            return "role0000";   //初始数据的时候 默认这个就是超级管理员
        }

        /// <summary>
        /// 原前端框架 KJ 管控调用
        /// </summary>
        /// <param name="roleGid"></param>
        /// <param name="menuGids"></param>
        /// <param name="hiddenBtns">该角色禁用按钮的集合，格式 Basepage$Pageurl$btnIds(btnIds逗号分隔)</param>
        /// <returns></returns>
        public PFActionResult SaveRoleMenue(string roleGid, List<string> menuGids, List<string> hiddenBtns)
        {
            try
            {
                using (var db = DB.Create())
                {
                    //先删除角色下的所有权限
                    int m = db.Deleteable<IFP_UM_ROLE_MENU>().Where(x => x.RoleGuid == roleGid).ExecuteCommand();
                    int c = 0;
                    foreach (string menuGid in menuGids)
                    {
                        IFP_UM_ROLE_MENU one = new IFP_UM_ROLE_MENU()
                        {
                            Gid = Guid.NewGuid().ToString("N"),
                            RoleGuid = roleGid,
                            MenuGuid = menuGid,
                            Delt = 0,
                            CreateTime = DateTime.Now
                        };
                        c += db.Insertable<IFP_UM_ROLE_MENU>(one).ExecuteCommand();
                    }

                    foreach (string one in hiddenBtns)
                    {
                        string[] ss = one.Split("$");
                        IFP_UM_ROLE_BTN btn = new IFP_UM_ROLE_BTN()
                        {
                            Gid = Guid.NewGuid().ToString("N"),
                            Roleid = roleGid,
                            Basepage = ss[0],
                            Pageurl = ss[1],
                            Buttons = ss[2]
                        };
                        //先删除角色下的所有权限
                        int b = db.Deleteable<IFP_UM_ROLE_BTN>().Where(
                            x => x.Roleid.Equals(btn.Roleid)
                            && x.Pageurl.Equals(btn.Pageurl)
                            && x.Basepage.Equals(btn.Basepage)
                            ).ExecuteCommand();
                        if (!string.IsNullOrEmpty(btn.Buttons.Value))
                        {
                            db.Insertable<IFP_UM_ROLE_BTN>(btn);
                        }
                    }
                    return new PFActionResult() { success = c == menuGids.Count, msg = $"传入权限{menuGids.Count}条，保存权限{c}条。" };
                }
            }
            catch (Exception e)
            {
                LoggerHelper.Error(ErrorList.E9999, e.Message, e);
                return new PFActionResult() { success = false, msg = e.Message };
            }
        }

        /// <summary>
        /// 新前端框架Pure Admin调用（增强版，支持权限同步）
        /// </summary>
        /// <param name="roleGid">角色ID</param>
        /// <param name="menuGids">菜单ID列表</param>
        /// <param name="hiddenBtns">隐藏按钮列表</param>
        /// <param name="enablePermissionSync">是否启用权限同步，默认true</param>
        /// <returns>保存结果</returns>
        public PFActionResult SaveRoleMenu(string roleGid, List<string> menuGids, List<string> hiddenBtns, bool enablePermissionSync = true)
        {
            try
            {
                using (var db = DB.Create())
                {
                    db.BeginTran();

                    //先删除角色下的所有权限
                    int m = db.Deleteable<IFP_UM_ROLE_MENU>().Where(x => x.RoleGuid == roleGid).ExecuteCommand();
                    int c = 0;
                    foreach (string menuGid in menuGids)
                    {
                        IFP_UM_ROLE_MENU one = new IFP_UM_ROLE_MENU()
                        {
                            Gid = Guid.NewGuid().ToString("N"),
                            RoleMenuGuid = "", // 可以为空或设置默认值
                            RoleGuid = roleGid, // 关键：确保这个字段有值
                            MenuGuid = menuGid,
                            Delt = 0,
                            CreateTime = DateTime.Now,
                            Creator = UserCache.GetUserID() // 设置创建者
                        };
                        c += db.Insertable<IFP_UM_ROLE_MENU>(one).ExecuteCommand();
                    }

                    // 清除旧的按钮权限
                    db.Deleteable<IFP_UM_ROLE_BTN>().Where(x => x.Roleid == roleGid).ExecuteCommand();

                    foreach (string one in hiddenBtns)
                    {
                        string[] ss = one.Split("$");
                        if (ss.Length >= 3) // 确保格式正确
                        {
                            IFP_UM_ROLE_BTN btn = new IFP_UM_ROLE_BTN()
                            {
                                Gid = Guid.NewGuid().ToString("N"),
                                Roleid = roleGid,
                                Basepage = ss[0],
                                Pageurl = ss[1],
                                Buttons = ss[2]
                            };

                            if (!string.IsNullOrEmpty(btn.Buttons.Value))
                            {
                                db.Insertable<IFP_UM_ROLE_BTN>(btn).ExecuteCommand();
                            }
                        }
                    }

                    // =================== 新增：权限同步逻辑 ===================
                    PFActionResult syncResult = null;
                    if (enablePermissionSync)
                    {
                        try
                        {
                            // 同步该角色的权限到所有用户
                            syncResult = SyncUserPermissionsFromRole(roleGid);

                            if (!syncResult.success)
                            {
                                LoggerHelper.Error($"角色 {roleGid} 权限保存成功，但同步到用户失败: {syncResult.msg}");
                            }
                            else
                            {
                                LoggerHelper.Info($"角色 {roleGid} 权限已同步到用户: {syncResult.msg}");
                            }
                        }
                        catch (Exception syncEx)
                        {
                            LoggerHelper.Error(ErrorList.E9999, $"角色 {roleGid} 权限同步异常: {syncEx.Message}", syncEx);
                            syncResult = new PFActionResult
                            {
                                success = false,
                                msg = $"权限同步失败: {syncEx.Message}"
                            };
                        }
                    }
                    // =========================================================

                    db.CommitTran();

                    var result = new PFActionResult()
                    {
                        success = c == menuGids.Count,
                        msg = $"传入权限{menuGids.Count}条，保存权限{c}条。"
                    };

                    // 如果启用了权限同步，在返回结果中包含同步信息
                    if (enablePermissionSync && syncResult != null)
                    {
                        result.data = new
                        {
                            MenuPermissionsSaved = c,
                            TotalMenuPermissions = menuGids.Count,
                            PermissionSyncEnabled = true,
                            SyncResult = new
                            {
                                Success = syncResult.success,
                                Message = syncResult.msg,
                                Data = syncResult.data
                            }
                        };

                        if (syncResult.success)
                        {
                            result.msg += $" 权限已同步到用户。";
                        }
                        else
                        {
                            result.msg += $" 权限同步失败: {syncResult.msg}";
                        }
                    }

                    return result;
                }
            }
            catch (Exception e)
            {
                LoggerHelper.Error(ErrorList.E9999, e.Message, e);
                return new PFActionResult() { success = false, msg = e.Message };
            }
        }

        public List<string> QueryRoleBtnGid(string roleGid, string basepage)
        {
            using (var db = DB.Create())
            {
                var q = db.Queryable<IFP_UM_ROLE_BTN>()
                    .Where(x => x.Roleid.Equals(roleGid) && x.Basepage.Equals(basepage));
                //LoggerHelper.Info(q.ToString());
                List<IFP_UM_ROLE_BTN> list = q.ToList();
                List<string> result = new List<string>();
                foreach (IFP_UM_ROLE_BTN one in list)
                {
                    if (!string.IsNullOrEmpty(one.Buttons.Value))
                    {
                        string[] ss = one.Buttons.Value.Split(",");
                        foreach (string s in ss)
                        {
                            if (!string.IsNullOrEmpty(s))
                            {
                                result.Add(one.Pageurl + "$" + s);
                            }
                        }
                    }

                }
                return result;
            }
        }
        public PFActionResult DelRoleByGid(string gid)
        {
            using (var db = DB.Create())
            {
                try
                {
                    // 开启事务
                    db.BeginTran();

                    // 删除角色
                    int r = db.Deleteable<IFP_UM_ROLE>()
                        .Where(x => x.Gid == gid)
                        .ExecuteCommand();

                    // 删除角色菜单关联
                    int m = db.Deleteable<IFP_UM_ROLE_MENU>()
                        .Where(x => x.RoleGuid == gid)
                        .ExecuteCommand();

                    // 删除用户角色关联
                    int u = db.Deleteable<IFP_UM_USER_ROLE>()
                        .Where(x => x.RoleGuid == gid)
                        .ExecuteCommand();

                    // 删除角色按钮关联
                    int b = db.Deleteable<IFP_UM_ROLE_BTN>()
                        .Where(x => x.Roleid == gid)
                        .ExecuteCommand();

                    // 提交事务
                    db.CommitTran();

                    return new PFActionResult
                    {
                        success = true,
                        msg = $"删除角色{r}个，对应{m}个菜单，{u}个用户。"
                    };
                }
                catch (Exception e)
                {
                    // 回滚事务
                    db.RollbackTran();

                    LoggerHelper.Error(ErrorList.E9999, e.Message, e);
                    return new PFActionResult
                    {
                        success = false,
                        msg = e.Message
                    };
                }
            }

        }

        public PFActionResult SaveRole(IFP_UM_ROLE role)
        {
            PFActionResult result = new PFActionResult();
            using (var db = DB.Create())
            {
                try
                {
                    // 开启事务，确保数据一致性
                    db.BeginTran();

                    if (string.IsNullOrEmpty(role.Gid.Value))
                    {
                        // 新增角色：生成角色编码和ID
                        // 达梦数据库兼容的查询：获取所有4位数字格式的角色编码
                        string getAllCodesSql = @"
                        SELECT RoleCode 
                        FROM IFP_UM_ROLE 
                        WHERE LENGTH(RoleCode) = 4 
                        AND RoleCode NOT LIKE '%[^0-9]%'
                        ORDER BY RoleCode";

                        var allCodes = db.Ado.SqlQuery<string>(getAllCodesSql);

                        int maxCode = 0;
                        foreach (var code in allCodes)
                        {
                            if (int.TryParse(code, out int numericCode))
                            {
                                if (numericCode > maxCode)
                                {
                                    maxCode = numericCode;
                                }
                            }
                        }

                        int newCode = maxCode + 1;
                        if (newCode > 9999)
                        {
                            LoggerHelper.Error("RoleCode超过4位限制");
                            result.success = false;
                            result.msg = "保存失败，角色编码已达到最大值(9999)";
                            db.RollbackTran();
                            return result;
                        }

                        role.RoleCode = newCode.ToString().PadLeft(4, '0');
                        role.Gid = "role" + role.RoleCode;
                        role.CreateTime = DateTime.Now;
                        role.Delt = 0;
                        role.Creator = UserCache.GetUserID(); // 设置创建者
                    }
                    else
                    {
                        // 修改角色：保持创建时间和创建者不变
                        var originalRole = db.Queryable<IFP_UM_ROLE>()
                            .Where(x => x.Gid == role.Gid)
                            .First();

                        if (originalRole != null)
                        {
                            role.CreateTime = originalRole.CreateTime;
                            role.Creator = originalRole.Creator;
                        }
                    }

                    // 检查生成的角色ID是否已存在
                    var existingRole = db.Queryable<IFP_UM_ROLE>()
                        .Where(x => x.Gid == role.Gid)
                        .First();

                    if (existingRole != null)
                    {
                        LoggerHelper.Info($"角色ID {role.Gid.Value} 已存在，将执行删除后重新插入");
                    }

                    // 删除旧数据（如果存在）
                    int deleteCount = db.Deleteable<IFP_UM_ROLE>()
                        .Where(x => x.Gid == role.Gid)
                        .ExecuteCommand();

                    // 插入新数据
                    int insertCount = db.Insertable(role).ExecuteCommand();

                    if (insertCount > 0)
                    {
                        db.CommitTran();
                        result.success = true;
                        result.msg = "保存成功";
                    }
                    else
                    {
                        db.RollbackTran();
                        result.success = false;
                        result.msg = "保存失败，插入数据失败";
                    }
                }
                catch (Exception ex)
                {
                    db.RollbackTran();
                    result.success = false;
                    result.msg = $"系统异常：{ex.Message}";
                }
            }
            return result;
        }

        private string NumberAdd(string number, int addNum)
        {
            int n = Convert.ToInt32(number);
            n += addNum;
            string newNumber = Convert.ToString(n);
            if (newNumber.Length >= number.Length)
            {
                return newNumber;
            }
            else
            {
                string format = "";
                for (int i = 0; i < number.Length; i++)
                {
                    format += "0";
                }
                return n.ToString(format);
            }
        }

        /// <summary>
        /// 保存RoleList，存查样柜使用
        /// </summary>
        /// <param name="searchVO"></param>
        /// <returns></returns>
        public PFActionResult RoleListSave(List<IFP_UM_ROLE> list)
        {
            PFActionResult result = new PFActionResult();
            try
            {
                using (var db = DB.Create())
                {
                    // 开启事务
                    db.BeginTran();

                    // 删除所有角色
                    db.Deleteable<IFP_UM_ROLE>().ExecuteCommand();

                    // 获取新角色的Gid列表
                    List<string> gidList = list.Select(x => x.Gid.Value).ToList();

                    // 删除不存在的角色的菜单关联
                    db.Deleteable<IFP_UM_ROLE_MENU>()
                        .Where(x => !gidList.Contains(x.RoleGuid.Value))
                        .ExecuteCommand();

                    // 批量插入新的角色记录
                    foreach (var role in list)
                    {
                        role.Delt = 0;
                    }
                    db.Insertable(list).ExecuteCommand();

                    // 提交事务
                    db.CommitTran();

                    result.success = true;
                    result.msg = "保存完成";
                }
            }
            catch (Exception e)
            {
                LoggerHelper.Error(ErrorList.E9999, e.Message, e);
                result.success = false;
                result.msg = "保存失败：" + e.ToString();
            }
            return result;
        }

        #region 新增按钮权限管理方法

        /// <summary>
        /// 获取页面按钮列表
        /// </summary>
        /// <param name="pageUrl">页面URL</param>
        /// <returns>按钮列表</returns>
        public List<ButtonInfo> GetPageButtons(string pageUrl)
        {
            using (var db = DB.Create())
            {
                var buttons = db.Queryable<IFP_SYSTEM_BUTTONS>()
                    .Where(x => x.PageUrl == pageUrl && x.Delt == 0 && x.IsEnabled == 1)
                    .OrderBy(x => x.Sort)
                    .Select(x => new ButtonInfo
                    {
                        ButtonId = (string)x.ButtonId,
                        ButtonName = (string)x.ButtonName,
                        ButtonGroup = (string)x.ButtonGroup,
                        ButtonType = (string)x.ButtonType,
                        Icon = (string)x.Icon,
                        Description = (string)x.Description,
                        Sort = (int)x.Sort,
                        IsEnabled = (int)x.IsEnabled == 1
                    })
                    .ToList();

                return buttons;
            }
        }

        /// <summary>
        /// 获取角色按钮权限配置
        /// </summary>
        /// <param name="roleGid">角色ID</param>
        /// <param name="pageUrl">页面URL，为空则获取所有页面</param>
        /// <returns>角色按钮权限配置</returns>
        public List<PageButtonPermission> GetRoleButtonPermissions(string roleGid, string pageUrl = "")
        {
            using (var db = DB.Create())
            {
                var query = db.Queryable<IFP_UM_ROLE_BTN>()
                    .Where(x => x.Roleid == roleGid);

                if (!string.IsNullOrWhiteSpace(pageUrl))
                {
                    query = query.Where(x => x.Basepage == pageUrl || x.Pageurl == pageUrl);
                }

                var roleButtons = query.ToList();
                var result = new List<PageButtonPermission>();

                foreach (var roleBtn in roleButtons)
                {
                    var permission = new PageButtonPermission
                    {
                        PageUrl = roleBtn.Pageurl.Value,
                        PageName = GetPageName(roleBtn.Pageurl.Value),
                        DeniedButtons = string.IsNullOrWhiteSpace(roleBtn.Buttons.Value)
                            ? new List<string>()
                            : roleBtn.Buttons.Value.Split(',').ToList(),
                        AllowedButtons = GetPageAllowedButtons(roleBtn.Pageurl.Value, roleBtn.Buttons.Value)
                    };

                    result.Add(permission);
                }

                return result;
            }
        }

        /// <summary>
        /// 保存角色按钮权限配置
        /// </summary>
        /// <param name="roleGid">角色ID</param>
        /// <param name="permissions">权限配置</param>
        /// <returns>保存结果</returns>
        public PFActionResult SaveRoleButtonPermissions(string roleGid, List<PageButtonPermission> permissions)
        {
            using (var db = DB.Create())
            {
                try
                {
                    // 开启事务
                    db.BeginTran();

                    // 删除角色的所有按钮权限配置
                    db.Deleteable<IFP_UM_ROLE_BTN>()
                        .Where(x => x.Roleid == roleGid)
                        .ExecuteCommand();

                    // 插入新的权限配置
                    foreach (var permission in permissions)
                    {
                        if (permission.DeniedButtons != null && permission.DeniedButtons.Count > 0)
                        {
                            var roleBtn = new IFP_UM_ROLE_BTN
                            {
                                Gid = Guid.NewGuid().ToString("N"),
                                Roleid = roleGid,
                                Basepage = permission.PageUrl,
                                Pageurl = permission.PageUrl,
                                Buttons = string.Join(",", permission.DeniedButtons)
                            };

                            db.Insertable(roleBtn).ExecuteCommand();
                        }
                    }

                    // 提交事务
                    db.CommitTran();

                    return new PFActionResult
                    {
                        success = true,
                        msg = "角色按钮权限保存成功"
                    };
                }
                catch (Exception ex)
                {
                    // 回滚事务
                    db.RollbackTran();
                    LoggerHelper.Error(ErrorList.E9999, $"保存角色按钮权限失败: {ex.Message}", ex);
                    return new PFActionResult
                    {
                        success = false,
                        msg = $"保存角色按钮权限失败：{ex.Message}"
                    };
                }
            }
        }

        /// <summary>
        /// 获取用户按钮权限
        /// </summary>
        /// <param name="userGid">用户ID</param>
        /// <param name="pageUrl">页面URL</param>
        /// <returns>用户按钮权限</returns>
        public List<UserButtonPermissionResult> GetUserButtonPermissions(string userGid, string pageUrl = "")
        {
            using (var db = DB.Create())
            {
                // 获取用户的所有角色
                var userRoles = db.Queryable<IFP_UM_USER_ROLE>()
                    .Where(x => x.UsiGuid == userGid && x.Delt == 0)
                    .Select(x => x.RoleGuid)
                    .ToList();

                if (userRoles.Count == 0)
                {
                    return new List<UserButtonPermissionResult>();
                }

                // 检查是否包含超级管理员角色
                if (userRoles.Contains(StaticConfig.SuperRoleId))
                {
                    return GetAllPagesButtonPermissions(pageUrl, true);
                }

                // 获取所有可能的页面列表
                List<string> targetPages = new List<string>();

                if (!string.IsNullOrWhiteSpace(pageUrl))
                {
                    // 如果指定了页面，只处理该页面
                    targetPages.Add(pageUrl);
                }
                else
                {
                    targetPages = db.Queryable<IFP_SYSTEM_BUTTONS>()
                                    .Where(x => x.Delt == 0 && x.IsEnabled == 1)
                                    .GroupBy(x => x.PageUrl)
                                    .Select(x => new { PageUrl = (string)x.PageUrl }) // ← 明确指定返回字段
                                    .ToList()
                                    .Select(x => x.PageUrl)
                                    .ToList();
                }

                // 获取所有角色的按钮权限配置
                var allRoleButtons = db.Queryable<IFP_UM_ROLE_BTN>()
                    .Where(x => userRoles.Contains(x.Roleid))
                    .ToList();

                var result = new List<UserButtonPermissionResult>();

                // 按页面逐一处理
                foreach (var page in targetPages)
                {
                    // 获取该页面的权限配置
                    var pageRoleButtons = allRoleButtons
                        .Where(x => x.Basepage.Value == page || x.Pageurl.Value == page)
                        .ToList();

                    var deniedButtons = new HashSet<string>();

                    // 如果有权限配置，收集禁用的按钮
                    if (pageRoleButtons.Count > 0)
                    {
                        foreach (var roleBtn in pageRoleButtons)
                        {
                            if (!string.IsNullOrWhiteSpace(roleBtn.Buttons.Value))
                            {
                                var buttons = roleBtn.Buttons.Value.Split(',');
                                foreach (var btn in buttons)
                                {
                                    if (!string.IsNullOrWhiteSpace(btn))
                                    {
                                        deniedButtons.Add(btn.Trim());
                                    }
                                }
                            }
                        }
                    }
                    else
                    {
                        LoggerHelper.Info($"页面 {page} 没有权限配置，默认允许所有按钮");
                    }

                    // 生成该页面的权限结果
                    var pageResult = new UserButtonPermissionResult
                    {
                        PageUrl = page,
                        DeniedButtons = deniedButtons.ToList(),
                        AllowedButtons = GetPageAllowedButtons(page, string.Join(",", deniedButtons))
                    };

                    result.Add(pageResult);
                }

                return result;
            }
        }

        /// <summary>
        /// 获取所有系统按钮定义
        /// </summary>
        /// <returns>系统按钮列表</returns>
        public List<IFP_SYSTEM_BUTTONS> GetAllSystemButtons()
        {
            using (var db = DB.Create())
            {
                return db.Queryable<IFP_SYSTEM_BUTTONS>()
                    .Where(x => x.Delt == 0)
                    .OrderBy(x => x.PageUrl)
                    .OrderBy(x => x.Sort)
                    .ToList();
            }
        }

        /// <summary>
        /// 保存系统按钮定义
        /// </summary>
        /// <param name="button">按钮定义</param>
        /// <returns>保存结果</returns>
        public PFActionResult SaveSystemButton(IFP_SYSTEM_BUTTONS button)
        {
            using (var db = DB.Create())
            {
                try
                {
                    if (string.IsNullOrWhiteSpace(button.Gid.Value))
                    {
                        // 新增
                        button.Gid = Guid.NewGuid().ToString("N");
                        button.CreateTime = DateTime.Now;
                        button.Creator = UserCache.GetUserID();
                        button.Delt = 0;
                        button.IsEnabled = 1;

                        db.Insertable(button).ExecuteCommand();
                    }
                    else
                    {
                        // 修改
                        button.UpdateTime = DateTime.Now;
                        button.Updater = UserCache.GetUserID();

                        db.Updateable(button).ExecuteCommand();
                    }

                    return new PFActionResult
                    {
                        success = true,
                        msg = "系统按钮保存成功"
                    };
                }
                catch (Exception ex)
                {
                    LoggerHelper.Error(ErrorList.E9999, $"保存系统按钮失败: {ex.Message}", ex);
                    return new PFActionResult
                    {
                        success = false,
                        msg = $"保存系统按钮失败：{ex.Message}"
                    };
                }
            }
        }

        /// <summary>
        /// 删除系统按钮定义
        /// </summary>
        /// <param name="buttonGid">按钮ID</param>
        /// <returns>删除结果</returns>
        public PFActionResult DeleteSystemButton(string buttonGid)
        {
            using (var db = DB.Create())
            {
                try
                {
                    // 软删除
                    db.Updateable<IFP_SYSTEM_BUTTONS>()
                        .SetColumns(x => new IFP_SYSTEM_BUTTONS
                        {
                            Delt = 1,
                            UpdateTime = DateTime.Now,
                            Updater = UserCache.GetUserID()
                        })
                        .Where(x => x.Gid == buttonGid)
                        .ExecuteCommand();

                    return new PFActionResult
                    {
                        success = true,
                        msg = "系统按钮删除成功"
                    };
                }
                catch (Exception ex)
                {
                    LoggerHelper.Error(ErrorList.E9999, $"删除系统按钮失败: {ex.Message}", ex);
                    return new PFActionResult
                    {
                        success = false,
                        msg = $"删除系统按钮失败：{ex.Message}"
                    };
                }
            }
        }

        /// <summary>
        /// 批量保存系统按钮
        /// </summary>
        /// <param name="buttons">按钮列表</param>
        /// <returns>保存结果</returns>
        public PFActionResult BatchSaveSystemButtons(List<IFP_SYSTEM_BUTTONS> buttons)
        {
            using (var db = DB.Create())
            {
                try
                {
                    db.BeginTran();

                    int successCount = 0;
                    foreach (var button in buttons)
                    {
                        // 检查是否已存在相同的按钮ID和页面URL组合
                        var existing = db.Queryable<IFP_SYSTEM_BUTTONS>()
                            .Where(x => x.ButtonId == button.ButtonId && x.PageUrl == button.PageUrl && x.Delt == 0)
                            .First();

                        if (existing == null)
                        {
                            db.Insertable(button).ExecuteCommand();
                            successCount++;
                        }
                        else
                        {
                            // 更新现有按钮
                            existing.ButtonName = button.ButtonName;
                            existing.ButtonGroup = button.ButtonGroup;
                            existing.ButtonType = button.ButtonType;
                            existing.Icon = button.Icon;
                            existing.Description = button.Description;
                            existing.Sort = button.Sort;
                            existing.UpdateTime = DateTime.Now;
                            existing.Updater = UserCache.GetUserID();

                            db.Updateable(existing).ExecuteCommand();
                            successCount++;
                        }
                    }

                    db.CommitTran();

                    return new PFActionResult
                    {
                        success = true,
                        msg = $"批量保存成功，共处理 {successCount} 个按钮"
                    };
                }
                catch (Exception ex)
                {
                    db.RollbackTran();
                    LoggerHelper.Error(ErrorList.E9999, $"批量保存系统按钮失败: {ex.Message}", ex);
                    return new PFActionResult
                    {
                        success = false,
                        msg = $"批量保存系统按钮失败：{ex.Message}"
                    };
                }
            }
        }

        /// <summary>
        /// 根据页面URL获取页面按钮权限码列表
        /// </summary>
        /// <param name="pageUrl">页面URL</param>
        /// <returns>权限码列表</returns>
        public List<string> GetPageButtonCodes(string pageUrl)
        {
            using (var db = DB.Create())
            {
                return db.Queryable<IFP_SYSTEM_BUTTONS>()
                    .Where(x => x.PageUrl == pageUrl && x.Delt == 0 && x.IsEnabled == 1)
                    .Select(x => x.ButtonId.Value)
                    .ToList();
            }
        }

        /// <summary>
        /// 检查用户是否拥有指定按钮权限
        /// </summary>
        /// <param name="userGid">用户ID</param>
        /// <param name="pageUrl">页面URL</param>
        /// <param name="buttonId">按钮ID</param>
        /// <returns>是否拥有权限</returns>
        public bool CheckUserButtonPermission(string userGid, string pageUrl, string buttonId)
        {
            using (var db = DB.Create())
            {
                // 获取用户角色
                var userRoles = db.Queryable<IFP_UM_USER_ROLE>()
                    .Where(x => x.UsiGuid == userGid && x.Delt == 0)
                    .Select(x => x.RoleGuid.Value)
                    .ToList();

                if (userRoles.Count == 0)
                {
                    return false; // 没有角色，无权限
                }

                // 检查是否为超级管理员
                if (userRoles.Contains(COM.IFP.Common.StaticConfig.SuperRoleId))
                {
                    return true; // 超级管理员拥有所有权限
                }

                // 检查是否在禁用列表中
                var deniedButtons = db.Queryable<IFP_UM_ROLE_BTN>()
                    .Where(x => userRoles.Contains(x.Roleid.Value) && x.Pageurl == pageUrl)
                    .Select(x => x.Buttons.Value)
                    .ToList();

                foreach (var denied in deniedButtons)
                {
                    if (!string.IsNullOrWhiteSpace(denied))
                    {
                        var deniedButtonIds = denied.Split(',').Select(x => x.Trim());
                        if (deniedButtonIds.Contains(buttonId))
                        {
                            return false; // 在禁用列表中
                        }
                    }
                }

                return true; // 不在禁用列表中，拥有权限
            }
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 获取页面名称
        /// </summary>
        /// <param name="pageUrl">页面URL</param>
        /// <returns>页面名称</returns>
        private string GetPageName(string pageUrl)
        {
            using (var db = DB.Create())
            {
                var menu = db.Queryable<IFP_UM_MENU>()
                    .Where(x => x.MenuUrl == pageUrl && x.Delt == 0)
                    .First();

                return menu?.MenuName.Value ?? "未知页面";
            }
        }

        /// <summary>
        /// 获取页面允许的按钮列表
        /// </summary>
        /// <param name="pageUrl">页面URL</param>
        /// <param name="deniedButtons">禁用的按钮</param>
        /// <returns>允许的按钮列表</returns>
        private List<string> GetPageAllowedButtons(string pageUrl, string deniedButtons)
        {
            using (var db = DB.Create())
            {
                var allButtonsData = db.Queryable<IFP_SYSTEM_BUTTONS>()
                    .Where(x => x.PageUrl == pageUrl && x.Delt == 0 && x.IsEnabled == 1)
                    .Select(x => new {
                        ButtonId = (string)x.ButtonId,  // 实际的按钮权限码
                        Gid = (string)x.Gid            // GID（用于调试）
                    })
                    .ToList();

                // 提取 ButtonId 列表
                var allButtons = allButtonsData.Select(x => x.ButtonId).ToList();

                if (string.IsNullOrWhiteSpace(deniedButtons))
                {
                    return allButtons;
                }

                var deniedList = deniedButtons.Split(',').Select(x => x.Trim()).ToList();
                var allowedButtons = allButtons.Where(x => !deniedList.Contains(x)).ToList();

                return allowedButtons;
            }
        }

        /// <summary>
        /// 获取所有页面的按钮权限（用于超级管理员）
        /// </summary>
        /// <param name="pageUrl">页面URL过滤</param>
        /// <param name="hasAllPermissions">是否拥有所有权限</param>
        /// <returns>权限结果</returns>
        private List<UserButtonPermissionResult> GetAllPagesButtonPermissions(string pageUrl, bool hasAllPermissions)
        {
            using (var db = DB.Create())
            {
                var query = db.Queryable<IFP_SYSTEM_BUTTONS>()
                    .Where(x => x.Delt == 0 && x.IsEnabled == 1);

                if (!string.IsNullOrWhiteSpace(pageUrl))
                {
                    query = query.Where(x => x.PageUrl == pageUrl);
                }

                var allButtons = query.ToList();
                var result = new List<UserButtonPermissionResult>();

                var pageGroups = allButtons.GroupBy(x => x.PageUrl.Value);
                foreach (var pageGroup in pageGroups)
                {
                    var pageButtons = pageGroup.Select(x => x.ButtonId.Value).ToList();

                    result.Add(new UserButtonPermissionResult
                    {
                        PageUrl = pageGroup.Key,
                        DeniedButtons = hasAllPermissions ? new List<string>() : pageButtons,
                        AllowedButtons = hasAllPermissions ? pageButtons : new List<string>()
                    });
                }

                return result;
            }
        }

        #endregion

        #region 权限继承同步机制

        /// <summary>
        /// 同步角色权限变更到用户权限表
        /// 当角色的菜单或按钮权限发生变化时，自动更新所有继承该角色的用户权限
        /// </summary>
        /// <param name="roleGid">角色ID</param>
        /// <returns>同步结果</returns>
        public PFActionResult SyncUserPermissionsFromRole(string roleGid)
        {
            using (var db = DB.Create())
            {
                try
                {
                    db.BeginTran();

                    // 添加调试日志
                    LoggerHelper.Info($"开始同步角色 {roleGid} 的权限到用户");

                    // 1. 获取该角色的所有用户 - 确保获取的是真实的用户GID
                    var userRoleRelations = db.Queryable<IFP_UM_USER_ROLE>()
                        .Where(x => (string)x.RoleGuid == roleGid && (int)x.Delt == 0)
                        .Select(x => new {
                            UserGid = (string)x.UsiGuid,  // 真实的用户GID
                            RelationGid = (string)x.Gid   // 用户角色关系的GID
                        })
                        .ToList();

                    LoggerHelper.Info($"角色 {roleGid} 下找到用户数量: {userRoleRelations.Count}");

                    if (userRoleRelations.Count == 0)
                    {
                        db.CommitTran();
                        return new PFActionResult
                        {
                            success = true,
                            msg = "该角色下没有用户，无需同步"
                        };
                    }

                    // 2. 验证用户GID的有效性
                    var userGids = userRoleRelations.Select(x => (string)x.UserGid).ToList();
                    var validUsers = db.Queryable<IFP_UM_USER_INFO>()
                        .Where(x => userGids.Contains((string)x.Gid) && (int)x.Delt == 0)
                        .Select(x => (string)x.Gid)
                        .ToList();

                    LoggerHelper.Info($"有效用户数量: {validUsers.Count}");

                    int totalUpdated = 0;

                    // 3. 为每个有效用户同步权限
                    foreach (var userGid in validUsers)
                    {
                        LoggerHelper.Info($"正在为用户 {userGid} 同步角色 {roleGid} 的权限");
                        var result = SyncSingleUserPermissionsFromRole(db, userGid, roleGid);
                        if (result.success)
                        {
                            totalUpdated++;
                            LoggerHelper.Info($"用户 {userGid} 权限同步成功: {result.msg}");
                        }
                        else
                        {
                            LoggerHelper.Error($"用户 {userGid} 权限同步失败: {result.msg}");
                        }
                    }

                    db.CommitTran();

                    var finalMsg = $"权限同步完成，共更新 {totalUpdated}/{validUsers.Count} 个用户的权限";
                    LoggerHelper.Info($"角色 {roleGid} {finalMsg}");

                    return new PFActionResult
                    {
                        success = true,
                        msg = finalMsg,
                        data = new
                        {
                            UpdatedUsers = totalUpdated,
                            TotalUsers = validUsers.Count,
                            InvalidUsers = userRoleRelations.Count - validUsers.Count
                        }
                    };
                }
                catch (Exception ex)
                {
                    db.RollbackTran();
                    LoggerHelper.Error(ErrorList.E9999, $"同步角色权限失败: {ex.Message}", ex);
                    return new PFActionResult
                    {
                        success = false,
                        msg = $"权限同步失败：{ex.Message}"
                    };
                }
            }
        }

        /// <summary>
        /// 为单个用户同步指定角色的权限
        /// </summary>
        /// <param name="db">数据库连接</param>
        /// <param name="userGid">用户ID</param>
        /// <param name="roleGid">角色ID</param>
        /// <returns>同步结果</returns>
        private PFActionResult SyncSingleUserPermissionsFromRole(SqlSugarClient db, string userGid, string roleGid)
        {
            try
            {
                LoggerHelper.Info($"开始为用户 {userGid} 同步角色 {roleGid} 的权限");

                // 1. 验证用户是否存在
                var userExists = db.Queryable<IFP_UM_USER_INFO>()
                    .Where(x => (string)x.Gid == userGid && (int)x.Delt == 0)
                    .Any();

                if (!userExists)
                {
                    return new PFActionResult
                    {
                        success = false,
                        msg = $"用户 {userGid} 不存在或已被删除"
                    };
                }

                // 2. 删除该用户从此角色继承的旧权限
                int deletedMenus = db.Deleteable<IFP_UM_USER_MENU>()
                    .Where(x => (string)x.UserGid == userGid && (string)x.SourceRoleGid == roleGid && (int)x.PermissionSource == 2)
                    .ExecuteCommand();

                int deletedButtons = db.Deleteable<IFP_UM_USER_BTN>()
                    .Where(x => (string)x.UserGid == userGid && (string)x.SourceRoleGid == roleGid && (int)x.PermissionSource == 2)
                    .ExecuteCommand();

                LoggerHelper.Info($"用户 {userGid} 删除旧权限: 菜单 {deletedMenus} 个，按钮 {deletedButtons} 个");

                // 3. 获取角色的最新菜单权限
                var roleMenus = db.Queryable<IFP_UM_ROLE_MENU>()
                    .Where(x => (string)x.RoleGuid == roleGid && (int)x.Delt == 0)
                    .ToList();

                LoggerHelper.Info($"角色 {roleGid} 的菜单权限数量: {roleMenus.Count}");

                // 4. 获取角色的最新按钮权限
                var roleButtons = db.Queryable<IFP_UM_ROLE_BTN>()
                    .Where(x => (string)x.Roleid == roleGid)
                    .ToList();

                LoggerHelper.Info($"角色 {roleGid} 的按钮权限数量: {roleButtons.Count}");

                int insertedMenus = 0;
                int insertedButtons = 0;

                // 5. 插入新的菜单权限（标记为角色继承）
                foreach (var roleMenu in roleMenus)
                {
                    // 检查用户是否已有该菜单的直接权限
                    var existingDirectPermission = db.Queryable<IFP_UM_USER_MENU>()
                        .Where(x => (string)x.UserGid == userGid && (string)x.MenuGid == (string)roleMenu.MenuGuid && (int)x.PermissionSource == 1)
                        .First();

                    // 如果用户没有直接权限，则添加继承权限
                    if (existingDirectPermission == null)
                    {
                        var userMenu = new IFP_UM_USER_MENU
                        {
                            Gid = Guid.NewGuid().ToString("N"),
                            UserGid = userGid,  // 确保使用正确的用户GID
                            MenuGid = roleMenu.MenuGuid.Value,
                            PermissionSource = 2,  // 2 表示角色继承
                            SourceRoleGid = roleGid,
                            CreateTime = DateTime.Now,
                            Creator = UserCache.GetUserID(),
                            Delt = 0
                        };

                        db.Insertable(userMenu).ExecuteCommand();
                        insertedMenus++;

                        LoggerHelper.Info($"为用户 {userGid} 添加继承菜单权限: {roleMenu.MenuGuid.Value}");
                    }
                }

                // 6. 插入新的按钮权限（标记为角色继承）
                LoggerHelper.Info($"开始为用户 {userGid} 同步按钮权限");

                // 先检查角色是否有按钮权限
                var roleButtonsQuery = db.Queryable<IFP_UM_ROLE_BTN>()
                    .Where(x => (string)x.Roleid == roleGid);

                var roleButtonsSql = roleButtonsQuery.ToSql(); // 获取SQL用于调试
                LoggerHelper.Info($"查询角色按钮权限SQL: {roleButtonsSql.Key}");

                var roleButtons2 = roleButtonsQuery.ToList();
                LoggerHelper.Info($"角色 {roleGid} 的按钮权限数量: {roleButtons2.Count}");

                if (roleButtons2.Count == 0)
                {
                    LoggerHelper.Warning($"角色 {roleGid} 没有配置任何按钮权限");
                }
                
                foreach (var roleButton in roleButtons2)
                {
                    LoggerHelper.Info($"处理角色按钮权限: GID={roleButton.Gid.Value}, 基础页面={roleButton.Basepage.Value}, 页面URL={roleButton.Pageurl.Value}, 按钮={roleButton.Buttons.Value}");

                    // 验证必要字段不为空
                    if (string.IsNullOrEmpty((string)roleButton.Pageurl))
                    {
                        LoggerHelper.Warning($"角色按钮权限的页面URL为空，跳过: GID={roleButton.Gid.Value}");
                        continue;
                    }

                    try
                    {
                        // 检查用户是否已有该页面的直接按钮权限
                        var existingDirectPermission = db.Queryable<IFP_UM_USER_BTN>()
                            .Where(x => (string)x.UserGid == userGid &&
                                       (string)x.Pageurl == (string)roleButton.Pageurl &&
                                       (int)x.PermissionSource == 1)
                            .First();

                        LoggerHelper.Info($"用户是否已有页面 {roleButton.Pageurl.Value} 的直接按钮权限: {existingDirectPermission != null}");

                        // 如果用户没有直接权限，则添加继承权限
                        if (existingDirectPermission == null)
                        {
                            var userButton = new IFP_UM_USER_BTN
                            {
                                Gid = Guid.NewGuid().ToString("N"),
                                UserGid = userGid,
                                Basepage = roleButton.Basepage.Value ?? "",
                                Pageurl = roleButton.Pageurl.Value,
                                Buttons = roleButton.Buttons.Value ?? "",
                                PermissionSource = 2,  // 2 表示角色继承
                                SourceRoleGid = roleGid,
                                CreateTime = DateTime.Now,
                                Creator = UserCache.GetUserID(),
                                Delt = 0
                            };

                            LoggerHelper.Info($"准备插入用户按钮权限: 用户={userButton.UserGid.Value}, 页面={userButton.Pageurl.Value}, 按钮={userButton.Buttons.Value}");

                            var insertResult = db.Insertable(userButton).ExecuteCommand();
                            insertedButtons++;

                            LoggerHelper.Info($"为用户 {userGid} 添加继承按钮权限成功: 页面={roleButton.Pageurl.Value}, 按钮={roleButton.Buttons.Value}, 插入结果={insertResult}");
                        }
                        else
                        {
                            LoggerHelper.Info($"用户 {userGid} 已有页面 {roleButton.Pageurl.Value} 的直接按钮权限，跳过继承权限添加");
                        }
                    }
                    catch (Exception btnEx)
                    {
                        LoggerHelper.Error($"为用户 {userGid} 处理页面 {roleButton.Pageurl.Value} 的按钮权限时发生异常: {btnEx.Message}");
                        LoggerHelper.Error($"异常堆栈: {btnEx.StackTrace}");
                    }
                }

                LoggerHelper.Info($"用户 {userGid} 权限同步完成: 新增菜单权限 {insertedMenus} 个，新增按钮权限 {insertedButtons} 个");

                return new PFActionResult
                {
                    success = true,
                    msg = $"用户 {userGid} 权限同步成功，菜单权限 {insertedMenus} 个，按钮权限 {insertedButtons} 个",
                    data = new { MenuPermissions = insertedMenus, ButtonPermissions = insertedButtons }
                };
            }
            catch (Exception ex)
            {
                LoggerHelper.Error(ErrorList.E9999, $"同步单个用户权限失败: {ex.Message}", ex);
                return new PFActionResult
                {
                    success = false,
                    msg = $"用户 {userGid} 权限同步失败：{ex.Message}"
                };
            }
        }

        /// <summary>
        /// 批量同步多个角色的权限到用户
        /// </summary>
        /// <param name="roleGids">角色ID列表</param>
        /// <returns>同步结果</returns>
        public PFActionResult BatchSyncUserPermissionsFromRoles(List<string> roleGids)
        {
            var results = new List<object>();
            int successCount = 0;

            foreach (var roleGid in roleGids)
            {
                var result = SyncUserPermissionsFromRole(roleGid);
                results.Add(new { RoleGid = roleGid, Result = result });

                if (result.success)
                {
                    successCount++;
                }
            }

            return new PFActionResult
            {
                success = successCount > 0,
                msg = $"批量同步完成，成功 {successCount}/{roleGids.Count} 个角色",
                data = results
            };
        }

        /// <summary>
        /// 当用户被分配新角色时，自动添加该角色的权限
        /// </summary>
        /// <param name="userGid">用户ID</param>
        /// <param name="roleGid">新分配的角色ID</param>
        /// <returns>操作结果</returns>
        public PFActionResult AddUserRolePermissions(string userGid, string roleGid)
        {
            using (var db = DB.Create())
            {
                try
                {
                    db.BeginTran();

                    // 验证角色是否存在
                    var role = db.Queryable<IFP_UM_ROLE>()
                        .Where(x => x.Gid == roleGid && x.Delt == 0)
                        .First();

                    if (role == null)
                    {
                        return new PFActionResult
                        {
                            success = false,
                            msg = "指定的角色不存在"
                        };
                    }

                    // 验证用户是否存在
                    var user = db.Queryable<IFP_UM_USER_INFO>()
                        .Where(x => x.Gid == userGid && x.Delt == 0)
                        .First();

                    if (user == null)
                    {
                        return new PFActionResult
                        {
                            success = false,
                            msg = "指定的用户不存在"
                        };
                    }

                    // 同步权限
                    var syncResult = SyncSingleUserPermissionsFromRole(db, userGid, roleGid);

                    if (syncResult.success)
                    {
                        db.CommitTran();
                        return new PFActionResult
                        {
                            success = true,
                            msg = $"用户 {user.UsiName.Value} 已成功获得角色 {role.RoleName.Value} 的权限",
                            data = syncResult.data
                        };
                    }
                    else
                    {
                        db.RollbackTran();
                        return syncResult;
                    }
                }
                catch (Exception ex)
                {
                    db.RollbackTran();
                    LoggerHelper.Error(ErrorList.E9999, $"添加用户角色权限失败: {ex.Message}", ex);
                    return new PFActionResult
                    {
                        success = false,
                        msg = $"添加用户角色权限失败：{ex.Message}"
                    };
                }
            }
        }

        /// <summary>
        /// 当用户被移除角色时，删除该角色的继承权限
        /// </summary>
        /// <param name="userGid">用户ID</param>
        /// <param name="roleGid">被移除的角色ID</param>
        /// <returns>操作结果</returns>
        public PFActionResult RemoveUserRolePermissions(string userGid, string roleGid)
        {
            using (var db = DB.Create())
            {
                try
                {
                    db.BeginTran();

                    // 删除该用户从此角色继承的权限
                    int deletedMenus = db.Deleteable<IFP_UM_USER_MENU>()
                        .Where(x => (string)x.UserGid == userGid && (string)x.SourceRoleGid == roleGid && (int)x.PermissionSource == (int)PermissionSourceEnum.RoleInherit)
                        .ExecuteCommand();

                    int deletedButtons = db.Deleteable<IFP_UM_USER_BTN>()
                        .Where(x => (string)x.UserGid == userGid && (string)x.SourceRoleGid == roleGid && (int)x.PermissionSource == (int)PermissionSourceEnum.RoleInherit)
                        .ExecuteCommand();

                    db.CommitTran();

                    return new PFActionResult
                    {
                        success = true,
                        msg = $"已移除用户从角色继承的权限：菜单权限 {deletedMenus} 个，按钮权限 {deletedButtons} 个",
                        data = new { MenuPermissions = deletedMenus, ButtonPermissions = deletedButtons }
                    };
                }
                catch (Exception ex)
                {
                    db.RollbackTran();
                    LoggerHelper.Error(ErrorList.E9999, $"移除用户角色权限失败: {ex.Message}", ex);
                    return new PFActionResult
                    {
                        success = false,
                        msg = $"移除用户角色权限失败：{ex.Message}"
                    };
                }
            }
        }

        /// <summary>
        /// 获取权限继承关系报告
        /// </summary>
        /// <param name="userGid">用户ID，为空则查询所有用户</param>
        /// <returns>权限继承报告</returns>
        public PFActionResult GetPermissionInheritanceReport(string userGid = "")
        {
            using (var db = DB.Create())
            {
                try
                {
                    var query = db.Queryable<IFP_UM_USER_MENU, IFP_UM_USER_INFO, IFP_UM_MENU, IFP_UM_ROLE>(
                        (um, u, m, r) => new JoinQueryInfos(
                            JoinType.Inner, um.UserGid == u.Gid,
                            JoinType.Inner, um.MenuGid == m.Gid,
                            JoinType.Left, um.SourceRoleGid == r.Gid
                        ))
                        .Where((um, u, m, r) => um.PermissionSource == (int)PermissionSourceEnum.RoleInherit);

                    if (!string.IsNullOrWhiteSpace(userGid))
                    {
                        query = query.Where((um, u, m, r) => u.Gid == userGid);
                    }

                    var menuInheritance = query.Select((um, u, m, r) => new
                    {
                        UserGid = u.Gid.Value,
                        UserName = u.UsiName.Value,
                        MenuGid = m.Gid.Value,
                        MenuName = m.MenuName.Value,
                        SourceRoleGid = r.Gid.Value,
                        SourceRoleName = r.RoleName.Value,
                        PermissionType = "Menu"
                    }).ToList();

                    var buttonQuery = db.Queryable<IFP_UM_USER_BTN, IFP_UM_USER_INFO, IFP_UM_ROLE>(
                        (ub, u, r) => new JoinQueryInfos(
                            JoinType.Inner, ub.UserGid == u.Gid,
                            JoinType.Left, ub.SourceRoleGid == r.Gid
                        ))
                        .Where((ub, u, r) => ub.PermissionSource == (int)PermissionSourceEnum.RoleInherit);

                    if (!string.IsNullOrWhiteSpace(userGid))
                    {
                        buttonQuery = buttonQuery.Where((ub, u, r) => u.Gid == userGid);
                    }

                    var buttonInheritance = buttonQuery.Select((ub, u, r) => new
                    {
                        UserGid = u.Gid.Value,
                        UserName = u.UsiName.Value,
                        PageUrl = ub.Pageurl.Value,
                        DeniedButtons = ub.Buttons.Value,
                        SourceRoleGid = r.Gid.Value,
                        SourceRoleName = r.RoleName.Value,
                        PermissionType = "Button"
                    }).ToList();

                    return new PFActionResult
                    {
                        success = true,
                        msg = "权限继承报告生成成功",
                        data = new
                        {
                            MenuInheritance = menuInheritance,
                            ButtonInheritance = buttonInheritance,
                            Summary = new
                            {
                                TotalMenuInheritance = menuInheritance.Count,
                                TotalButtonInheritance = buttonInheritance.Count,
                                AffectedUsers = menuInheritance.Select(x => x.UserGid)
                                    .Union(buttonInheritance.Select(x => x.UserGid))
                                    .Distinct().Count()
                            }
                        }
                    };
                }
                catch (Exception ex)
                {
                    LoggerHelper.Error(ErrorList.E9999, $"生成权限继承报告失败: {ex.Message}", ex);
                    return new PFActionResult
                    {
                        success = false,
                        msg = $"生成权限继承报告失败：{ex.Message}"
                    };
                }
            }
        }
        #endregion

        /// <summary>
        /// 更新用户角色（增强版，支持权限同步）
        /// </summary>
        /// <param name="gid">用户ID</param>
        /// <param name="roleIds">角色ID列表（逗号分隔）</param>
        /// <param name="enablePermissionSync">是否启用权限同步，默认true</param>
        /// <returns>更新结果</returns>
        public PFActionResult UpdateUserRoleEnhanced(string gid, string roleIds, bool enablePermissionSync = true)
        {
            using (var db = DB.Create())
            {
                try
                {
                    db.BeginTran();

                    var syncResults = new List<object>();

                    if (enablePermissionSync)
                    {
                        // 1. 获取用户当前的角色
                        var currentRoles = db.Queryable<IFP_UM_USER_ROLE>()
                            .Where(x => x.UsiGuid == gid && x.Delt == 0)
                            .Select(x => x.RoleGuid.Value)
                            .ToList();

                        // 2. 解析新的角色列表
                        var newRoles = string.IsNullOrWhiteSpace(roleIds)
                            ? new List<string>()
                            : roleIds.Split(',').Where(x => !string.IsNullOrWhiteSpace(x)).ToList();

                        // 3. 找出被移除的角色和新增的角色
                        var removedRoles = currentRoles.Except(newRoles).ToList();
                        var addedRoles = newRoles.Except(currentRoles).ToList();

                        // 4. 先移除旧角色的继承权限
                        foreach (var removedRole in removedRoles)
                        {
                            var removeResult = RemoveUserRolePermissions(gid, removedRole);
                            syncResults.Add(new
                            {
                                Action = "Remove",
                                RoleGid = removedRole,
                                Success = removeResult.success,
                                Message = removeResult.msg
                            });

                            if (!removeResult.success)
                            {
                                LoggerHelper.Error($"移除角色 {removedRole} 的权限失败: {removeResult.msg}");
                            }
                        }

                        // 5. 更新用户角色关系（这里复用原有逻辑）
                        db.Deleteable<IFP_UM_USER_ROLE>().Where(a => a.UsiGuid == gid).ExecuteCommand();

                        foreach (string strRole in newRoles)
                        {
                            db.Insertable(new IFP_UM_USER_ROLE()
                            {
                                Gid = Guid.NewGuid().ToString("N"),
                                UsiGuid = gid,
                                RoleGuid = strRole,
                                Delt = 0,
                                CreateTime = DateTime.Now,
                                Creator = UserCache.GetUserID()
                            }).ExecuteCommand();
                        }

                        // 6. 为新增角色添加继承权限
                        foreach (var addedRole in addedRoles)
                        {
                            var addResult = AddUserRolePermissions(gid, addedRole);
                            syncResults.Add(new
                            {
                                Action = "Add",
                                RoleGid = addedRole,
                                Success = addResult.success,
                                Message = addResult.msg
                            });

                            if (!addResult.success)
                            {
                                LoggerHelper.Error($"添加角色 {addedRole} 的权限失败: {addResult.msg}");
                            }
                        }

                        db.CommitTran();

                        return new PFActionResult
                        {
                            success = true,
                            msg = $"用户角色更新成功，移除角色 {removedRoles.Count} 个，新增角色 {addedRoles.Count} 个",
                            data = new
                            {
                                RemovedRoles = removedRoles,
                                AddedRoles = addedRoles,
                                CurrentRoles = newRoles,
                                SyncResults = syncResults,
                                PermissionSyncEnabled = true
                            }
                        };
                    }
                    else
                    {
                        // 不启用权限同步时，使用原有的简单逻辑
                        db.Deleteable<IFP_UM_USER_ROLE>().Where(a => a.UsiGuid == gid).ExecuteCommand();

                        if (!string.IsNullOrWhiteSpace(roleIds))
                        {
                            string[] arrRole = roleIds.Split(",");
                            foreach (string strRole in arrRole)
                            {
                                db.Insertable(new IFP_UM_USER_ROLE()
                                {
                                    Gid = Guid.NewGuid().ToString("N"),
                                    UsiGuid = gid,
                                    RoleGuid = strRole,
                                    Delt = 0,
                                    CreateTime = DateTime.Now,
                                    Creator = UserCache.GetUserID()
                                }).ExecuteCommand();
                            }
                        }

                        db.CommitTran();

                        return new PFActionResult
                        {
                            success = true,
                            msg = "用户角色更新成功",
                            data = new
                            {
                                PermissionSyncEnabled = false
                            }
                        };
                    }
                }
                catch (Exception ex)
                {
                    db.RollbackTran();
                    LoggerHelper.Error(ErrorList.E9999, $"更新用户角色失败: {ex.Message}", ex);
                    return new PFActionResult
                    {
                        success = false,
                        msg = $"更新用户角色失败：{ex.Message}"
                    };
                }
            }
        }

    }

    public class VUserRole
    {
        public string RoleID { get; set; }
        public string RoleName { get; set; }
        public string HasRole { get; set; }
    }

}
