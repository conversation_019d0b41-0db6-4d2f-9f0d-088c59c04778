﻿using COM.IFP.Common;
using COM.IFP.Log;
using Newtonsoft.Json;
using ORM.IFP.www.DbModel.UM;
using System;
using System.Collections.Generic;
using System.Text.Json;

namespace API.IFP.Rights
{
    /// <summary>
    /// 用户扩展信息接口 - 新项目专用
    /// 提供用户扩展信息的增删改查功能，包括职位、职称、电子签名等
    /// </summary>
    public class UserExt
    {
        private Lazy<DAL.IFP.Rights.UserExt> _service = Entity.Create<DAL.IFP.Rights.UserExt>();

        /// <summary>
        /// 查询用户扩展信息
        /// 支持分页和多条件查询
        /// </summary>
        /// <param name="json">查询条件，格式：{"filter": [...], "paging": {...}}</param>
        /// <returns>分页查询结果</returns>
        public PageModel<IFP_UM_USER_INFO> SelectUserExt(JsonElement json)
        {
            try
            {
                var filter = json.GetValue<IList<IFP_UM_USER_INFO>>("filter");
                var paging = json.GetValue<PageModel>("paging");

                LoggerHelper.Info($"查询用户扩展信息，过滤条件数量: {filter?.Count ?? 0}");

                var result = _service.Value.SelectUserExt(filter, paging);
                return result;
            }
            catch (Exception ex)
            {
                LoggerHelper.Error(ErrorList.E9999, $"查询用户扩展信息失败: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 保存用户扩展信息（新增或更新）
        /// 自动处理扩展信息的JSON序列化
        /// </summary>
        /// <param name="json">用户扩展信息数据</param>
        /// <returns>操作结果</returns>
        public PFActionResult SaveUserExt(JsonElement json)
        {
            try
            {
                // 创建序列化设置并添加Newtonsoft的JSON转换器
                var settings = new JsonSerializerSettings
                {
                    Converters = new List<JsonConverter> { new NewtonsoftFieldConverter() }
                };

                // 使用自定义设置进行反序列化
                var userInfo = JsonConvert.DeserializeObject<IFP_UM_USER_INFO>(json.ToString(), settings);

                LoggerHelper.Info($"保存用户扩展信息，用户ID: {userInfo.Gid.Value}");

                return _service.Value.SaveUserExt(userInfo);
            }
            catch (Exception ex)
            {
                LoggerHelper.Error(ErrorList.E9999, $"保存用户扩展信息失败: {ex.Message}", ex);
                return new PFActionResult
                {
                    success = false,
                    msg = "保存用户扩展信息失败：" + ex.Message
                };
            }
        }

        /// <summary>
        /// 根据用户ID获取扩展信息
        /// </summary>
        /// <param name="json">包含用户ID的参数，格式：{"userGid": "用户ID"}</param>
        /// <returns>用户扩展信息</returns>
        public PFActionResult GetUserExtByGid(JsonElement json)
        {
            try
            {
                string userGid = json.GetValue<string>("userGid");

                if (string.IsNullOrWhiteSpace(userGid))
                {
                    return new PFActionResult
                    {
                        success = false,
                        msg = "用户ID不能为空"
                    };
                }

                LoggerHelper.Info($"获取用户扩展信息，用户ID: {userGid}");

                return _service.Value.GetUserExtByGid(userGid);
            }
            catch (Exception ex)
            {
                LoggerHelper.Error(ErrorList.E9999, $"获取用户扩展信息失败: {ex.Message}", ex);
                return new PFActionResult
                {
                    success = false,
                    msg = "获取用户扩展信息失败：" + ex.Message
                };
            }
        }

        /// <summary>
        /// 删除用户（软删除）
        /// </summary>
        /// <param name="json">包含用户ID的参数，格式：{"userGid": "用户ID"}</param>
        /// <returns>删除结果</returns>
        public PFActionResult DeleteUserExt(JsonElement json)
        {
            try
            {
                string userGid = json.GetValue<string>("userGid");

                if (string.IsNullOrWhiteSpace(userGid))
                {
                    return new PFActionResult
                    {
                        success = false,
                        msg = "用户ID不能为空"
                    };
                }

                LoggerHelper.Info($"删除用户，用户ID: {userGid}");

                return _service.Value.DeleteUserExt(userGid);
            }
            catch (Exception ex)
            {
                LoggerHelper.Error(ErrorList.E9999, $"删除用户失败: {ex.Message}", ex);
                return new PFActionResult
                {
                    success = false,
                    msg = "删除用户失败：" + ex.Message
                };
            }
        }

        /// <summary>
        /// 更新用户扩展信息字段
        /// 仅更新ExtInfo字段，不影响其他用户基础信息
        /// </summary>
        /// <param name="json">
        /// 更新参数，格式：
        /// {
        ///   "userGid": "用户ID",
        ///   "extInfo": {
        ///     "position": "职位",
        ///     "title": "职称", 
        ///     "signature": "电子签名Base64",
        ///     "others": {...}
        ///   }
        /// }
        /// </param>
        /// <returns>更新结果</returns>
        public PFActionResult UpdateUserExtInfo(JsonElement json)
        {
            try
            {
                string userGid = json.GetValue<string>("userGid");

                if (string.IsNullOrWhiteSpace(userGid))
                {
                    return new PFActionResult
                    {
                        success = false,
                        msg = "用户ID不能为空"
                    };
                }

                // 解析扩展信息
                UserExtInfo extInfo = null;
                if (json.TryGetProperty("extInfo", out JsonElement extInfoElement))
                {
                    var extInfoJson = extInfoElement.GetRawText();
                    extInfo = JsonConvert.DeserializeObject<UserExtInfo>(extInfoJson);
                }

                LoggerHelper.Info($"更新用户扩展信息字段，用户ID: {userGid}");

                return _service.Value.UpdateUserExtInfo(userGid, extInfo);
            }
            catch (Exception ex)
            {
                LoggerHelper.Error(ErrorList.E9999, $"更新用户扩展信息字段失败: {ex.Message}", ex);
                return new PFActionResult
                {
                    success = false,
                    msg = "更新用户扩展信息字段失败：" + ex.Message
                };
            }
        }

        /// <summary>
        /// 批量导入用户扩展信息
        /// </summary>
        /// <param name="json">批量用户数据</param>
        /// <returns>导入结果</returns>
        public PFActionResult BatchImportUserExt(JsonElement json)
        {
            try
            {
                var settings = new JsonSerializerSettings
                {
                    Converters = new List<JsonConverter> { new NewtonsoftFieldConverter() }
                };

                var userList = JsonConvert.DeserializeObject<List<IFP_UM_USER_INFO>>(json.ToString(), settings);

                if (userList == null || userList.Count == 0)
                {
                    return new PFActionResult
                    {
                        success = false,
                        msg = "用户数据不能为空"
                    };
                }

                LoggerHelper.Info($"批量导入用户扩展信息，数量: {userList.Count}");

                return _service.Value.BatchImportUserExt(userList);
            }
            catch (Exception ex)
            {
                LoggerHelper.Error(ErrorList.E9999, $"批量导入用户扩展信息失败: {ex.Message}", ex);
                return new PFActionResult
                {
                    success = false,
                    msg = "批量导入用户扩展信息失败：" + ex.Message
                };
            }
        }

        /// <summary>
        /// 获取用户列表（带扩展信息）
        /// 用于下拉选择等场景
        /// </summary>
        /// <param name="json">查询参数，可包含部门过滤等</param>
        /// <returns>用户列表</returns>
        public PFActionResult GetUserExtList(JsonElement json)
        {
            try
            {
                // 可选的部门过滤
                string deptGid = "";
                if (json.TryGetProperty("deptGid", out JsonElement deptElement))
                {
                    deptGid = deptElement.GetString();
                }

                // 可选的状态过滤（启用/禁用）
                int? status = null;
                if (json.TryGetProperty("status", out JsonElement statusElement))
                {
                    status = statusElement.GetInt32();
                }

                LoggerHelper.Info($"获取用户扩展信息列表，部门: {deptGid}, 状态: {status}");

                return _service.Value.GetUserExtList(deptGid, status);
            }
            catch (Exception ex)
            {
                LoggerHelper.Error(ErrorList.E9999, $"获取用户扩展信息列表失败: {ex.Message}", ex);
                return new PFActionResult
                {
                    success = false,
                    msg = "获取用户扩展信息列表失败：" + ex.Message
                };
            }
        }
    }
}