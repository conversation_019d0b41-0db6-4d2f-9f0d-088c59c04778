﻿<!DOCTYPE html>
<html lang="zh-Hans">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>定时计划配置</title>
</head>
<body class="flex" controller option="{platform:'element'}">
    <ifp-page class="flex flex-item" id="app" style="width:100%;height:580px">
        <ifp-toolbar close>
            <ifp-button code="B1" v-if="!$btnRight.B1" @click="sumbit()">确认</ifp-button>
        </ifp-toolbar>
        <div class="flex-item flex margin">
            <el-tabs v-model="activeTab" @tab-click="handleTabClick" style="height:220px">
                <el-tab-pane label="小时" name="hour">
                    <el-form>
                        <el-form-item>
                            <el-row>
                                <el-col :span="24">
                                    <el-radio-group v-model="hourSelection">
                                        <el-radio :label="0">每小时</el-radio>
                                    </el-radio-group>
                                </el-col>
                                <el-col :span="3">
                                    <el-radio-group v-model="hourSelection">
                                        <el-radio :label="1">按指定小时</el-radio>
                                    </el-radio-group>
                                </el-col>
                                <el-col :span="21">
                                    <el-checkbox-group v-model="selectedHours">
                                        <el-checkbox :label="String(index)" v-for="index in 24" :key="index">{{ index }}</el-checkbox>
                                    </el-checkbox-group>
                                </el-col>
                            </el-row>

                        </el-form-item>
                    </el-form>
                </el-tab-pane>

                <el-tab-pane label="日" name="day">
                    <el-form>
                        <el-form-item>
                            <el-row>
                                <el-col :span="24">
                                    <el-radio-group v-model="daySelection">
                                        <el-radio :label="0">每日</el-radio>
                                    </el-radio-group>
                                </el-col>
                                <el-col :span="3">
                                    <el-radio-group v-model="daySelection">
                                        <el-radio :label="1">按指定日</el-radio>
                                    </el-radio-group>
                                </el-col>
                                <el-col :span="21">
                                    <el-checkbox-group v-model="selectedDays">
                                        <el-checkbox :label="index" v-for="index in 31" :key="index">{{ index }}</el-checkbox>
                                    </el-checkbox-group>
                                </el-col>
                                <el-col :span="24">
                                    <el-radio-group v-model="daySelection">
                                        <el-radio :label="2">按本月最后一天</el-radio>
                                    </el-radio-group>
                                </el-col>
                                <el-col :span="24">
                                    <el-radio-group v-model="daySelection">
                                        <el-radio :label="3">不指定</el-radio>
                                    </el-radio-group>
                                </el-col>
                            </el-row>
                        </el-form-item>
                    </el-form>
                </el-tab-pane>

                <el-tab-pane label="月" name="month">
                    <el-form>
                        <el-form-item>
                            <el-row>
                                <el-col :span="24">
                                    <el-radio-group v-model="monthSelection">
                                        <el-radio :label="0">每月</el-radio>
                                    </el-radio-group>
                                </el-col>
                                <el-col :span="3">
                                    <el-radio-group v-model="monthSelection">
                                        <el-radio :label="1">按指定月</el-radio>
                                    </el-radio-group>
                                </el-col>
                                <el-col :span="21">
                                    <el-checkbox-group v-model="selectedMonths">
                                        <el-checkbox :label="index" v-for="index in 12" :key="index">{{ index }}</el-checkbox>
                                    </el-checkbox-group>
                                </el-col>
                                <el-col :span="3">
                                    <el-radio-group v-model="monthSelection">
                                        <el-radio :label="2">按月循环</el-radio>
                                    </el-radio-group>
                                </el-col>
                                <el-col :span="21">
                                    <el-select v-model="startMonth" placeholder="从">
                                        <el-option :label="index" :value="index" v-for="index in 12" :key="index"></el-option>
                                    </el-select>
                                    <span>月开始，每</span>
                                    <el-select v-model="cycleMonth" placeholder="每">
                                        <el-option :label="index" :value="index" v-for="index in 12" :key="index"></el-option>
                                    </el-select>
                                    <span>月执行一次</span>
                                </el-col>
                            </el-row>
                        </el-form-item>
                    </el-form>
                </el-tab-pane>

                <el-tab-pane label="周（星期）" name="week">
                    <el-form>
                        <el-form-item>
                            <el-row>
                                <el-col :span="24">
                                    <el-radio-group v-model="weekSelection">
                                        <el-radio :label="0">不指定</el-radio>
                                    </el-radio-group>
                                </el-col>
                                <el-col :span="3">
                                    <el-radio-group v-model="weekSelection">
                                        <el-radio :label="1">按指定</el-radio>
                                    </el-radio-group>
                                </el-col>
                                <el-col :span="21">
                                    <el-checkbox-group v-model="selectedWeekDays">
                                        <el-checkbox :label="index + 1" v-for="index in 6" :key="index + 1"> {{xqList[index]}}</el-checkbox>
                                        <el-checkbox :label="1">星期天</el-checkbox>
                                    </el-checkbox-group>
                                </el-col>
                                <el-col :span="3">
                                    <el-radio-group v-model="weekSelection">
                                        <el-radio :label="2">按周循环</el-radio>
                                    </el-radio-group>
                                </el-col>
                                <el-col :span="21">
                                    <span>从第</span>
                                    <el-select v-model="startWeek" style="width:100px">
                                        <el-option :label="String(index)" :value="index" v-for="index in 4" :key="index"></el-option>
                                    </el-select>
                                    <span>周的</span>
                                    <el-select v-model="selectedDayOfWeek" style="width:100px">
                                        <el-option :label="xqList[index]" :value="index + 1" v-for="index in 6" :key="index + 1"></el-option>
                                        <el-option label="星期天" :value="1"></el-option>
                                    </el-select>
                                </el-col>
                                <el-col :span="3">
                                    <el-radio-group v-model="weekSelection">
                                        <el-radio :label="3">按本月最后一个</el-radio>
                                    </el-radio-group>
                                </el-col>
                                <el-col :span="21">
                                    <el-select v-model="selectedLastDayOfWeek" style="width:100px">
                                        <el-option :label="xqList[index]" :value="index + 1" v-for="index in 6" :key="index + 1"></el-option>
                                        <el-option label="星期天" :value="1"></el-option>
                                    </el-select>
                                </el-col>
                            </el-row>
                        </el-form-item>
                    </el-form>
                </el-tab-pane>
            </el-tabs>
        </div>
        <el-card class="flex" header="" style="height:70px">
            <ifp-form label-width="100px" :inline="true" class="flex-row">
                <ifp-form-item label="验证时间：">
                    <el-time-picker v-model="executionTime" :clearable="false" style="width:150px"></el-time-picker>
                </ifp-form-item>
                <ifp-form-item label="corn表达式：">
                    <el-input v-model="cornExpression" style="width:200px"></el-input>
                </ifp-form-item>
                <el-button @click="validateCron">获取从当前日期验证时间往后五次执行时刻</el-button>
            </ifp-form>
        </el-card>
        <div class="flex" style="height:200px">
            <el-card class="flex-item flex" header="最近五次执行时刻">
                <div v-for="item in next5">{{item}}</div>
            </el-card>
        </div>
    </ifp-page>
    <script src="/iofp/ics/starter.js"></script>
</body>
</html>