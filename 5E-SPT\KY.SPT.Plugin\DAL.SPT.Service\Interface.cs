﻿#define debug
using COM.IFP.Common;
using DAL.SPT.Service.Diagram;
using ORM.SPT;

namespace DAL.SPT.Service
{
    /// <summary>
    /// 外部程序集都从这里调用
    /// </summary>
    public class Interface
    {
        public bool CPUReady
        {
            get
            {
                if (Global.CPU == null)
                {
                    return false;
                }
                return true;
            }
        }

        public bool PlcIsConnected
        {
            get
            {
                return Global.PlcIsConnected;
            }
        }
        public bool RunStatus
        {
            get
            {
                return Global.RUN_STATUS;
            }
        }

        public bool AutoMode
        {
            get
            {
                return Global.CheckPointValue("01" + Constant.ZDYXML, true);
            }
        }

        public object GetTaskLineData
        {
            get
            {
                return Global.CurrentPipelineProgressData;
            }
        }
        public object GetAlarmList
        {
            get
            {
                return Global.AlarmListData;
            }
        }

        public object GetUnfinishedTaskList
        {
            get
            {
                return Global.UnfinishedTaskListDatas;
            }
        }

        public object GetRunInfoList
        {
            get
            {
                return Global.RunInfoListData;
            }
        }
        public Dictionary<string, object> GetPoint(out Dictionary<string, List<AlarmRunView>> alarmRunViews)
        {
            Dictionary<string, object> data = new Dictionary<string, object>();
            alarmRunViews = new Dictionary<string, List<AlarmRunView>>();

            var alarms = new List<AlarmRunView>();
            var runs = new List<AlarmRunView>();

            try
            {
                List<SPT_STATION> stations = Global.DB_Option.Value.SelectValidStation();

                List<string> str_stations = stations.Select(x => x.CODE.Value).ToList();

                foreach (var it in str_stations)
                {
                    alarmRunViews.Add(it, new List<AlarmRunView>());
                }

                foreach (var p in Global.DataMemory.Data.Values)
                {
                    try
                    {
                        if (str_stations.Contains(p.Station) == false)
                        {
                            continue;
                        }

                        data.Add(p.Name, p.Value);
#if debug

#else

                        if (p.PointType != typeof(bool) || p.Value == null || (bool)p.Value == false)
                        {
                            continue;
                        }
                        else
#endif

                        //todo数据表的点位类型简化
                        {
                            AlarmRunView tmp = new AlarmRunView
                            {
                                //Station = p.Station,
                                Name = p.Define,
                            };
                            if (p.PointDefine.ALARM.Value == 1 || p.PointDefine.FAULT.Value == 1)
                            {
                                tmp.Value = 2;
                                alarmRunViews[p.Station].Add(tmp);

                            }
                            else if (p.PointDefine.STATUS.Value == 1)
                            {
                                tmp.Value = 1;
                                alarmRunViews[p.Station].Add(tmp);
                            }
                        }
                    }
                    catch(Exception e1)
                    {
                        LogUtility.ToError("推送实时点数据，报警数据出错", e1);
                    }


                }

                //alarmRunViews.AddRange(alarms);
                //alarmRunViews.AddRange(runs);
            }
            catch (Exception e)
            {
                LogUtility.ToError("推送实时点数据，报警数据出错", e);
            }
            return data;
        }

        public PFActionResult InsertCmd(string CMD, Dictionary<string, object> jo)
        {
            return Global.CPU.InsertCmd(CMD, jo);
        }

        public object GetDiagram()
        {
            var data = Service.Diagram.ServiceDiagram.GetDiagram();

            if (data == null)
            {
                throw new Exception("站点管路图未初始化");
                return null;
            }
            else
            {
                return data;

            }
        }
        public PFActionResult SetDiagram()
        {
            return Service.Diagram.ServiceDiagram.SetDiagram();
        }
    }
}
