﻿using COM.IFP.Common;
using COM.IFP.Common.Secure;
using COM.IFP.Log;
using COM.IFP.SqlSugarN;
using ORM.IFP.ViewModel;
using ORM.IFP.www.DbModel.UM;
using ORM.IFP.www.DTO;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using PageModel = COM.IFP.Common.PageModel;

namespace DAL.IFP.Rights
{
    public class User
    {
        /// <summary>
        /// 通过登录名和密码查找用户
        /// </summary>
        /// <param name="userid">登录名</param>
        /// <param name="pwd"></param>
        /// <returns></returns>
        public IFP_UM_USER_INFO GetUserByIdPwd(string userid, string pwd)
        {
            using (var db = COM.IFP.SqlSugarN.DB.Create())
            {
                List<IFP_UM_USER_INFO> q2 = db.Queryable<IFP_UM_USER_INFO>()
                                          .Where(x => x.UsiLoginName == userid && x.UsiPassword == pwd.ToUpper())
                                          .ToList();
                if (q2.Count > 0)
                {
                    LoggerHelper.Info($"查询到用户:{q2.Count}");
                    return q2[0];
                }
                else
                {
                    LoggerHelper.Info($"未查询到用户");
                    return null;
                }
            }
        }

        /// <summary>
        /// 请求服务器时间
        /// </summary>
        /// <returns></returns>
        public int ProgramStartTime()
        {
            DateTime target = Config.ProgramStartTime;
            if (target.Kind == DateTimeKind.Unspecified)
                target = target.ToLocalTime();
            return (int)((target.ToUniversalTime().Ticks - 621355968000000000) / 10000000);
        }

        /// <summary>
        /// 用来得到用户名与ID的映射关系
        /// </summary>
        /// <param name="json"></param>
        /// <returns></returns>
        public List<IdTextModel> GetUserIdTextList()
        {
            using (var db = DB.Create())
            {
                var q1 = db.Queryable<IFP_UM_USER_INFO>().ToList();
                List<IdTextModel> result = new List<IdTextModel>();
                foreach (var i in q1)
                {
                    result.Add(new IdTextModel
                    {
                        id = i.Gid,
                        text = i.UsiName.Value,
                        sxh = i.UsiNumber.Value
                    });
                }
                return result;
            }
        }
        public object GetUserList(IList<IFP_UM_USER_INFO> filter, PageModel paging)
        {
            using (var db = DB.Create())
            {
                ISugarQueryable<IFP_UM_USER_INFO> q = db.Queryable<IFP_UM_USER_INFO>();
                if (filter != null)
                {
                    q = q.Query(filter.ToArray());
                }


                if (paging == null)
                {
                    var res = q.ToList();
                    return res;
                }
                else
                {
                    var res = q.Fetch(paging);
                    return res;
                }
            }
        }

        private IFP_UM_USER_INFO IofpUpdateOrSaveUser(IFP_UM_USER_INFO user)
        {
            using (var db = DB.Create())
            {
                string loginname = user.UsiLoginName.Value;

                // 尝试更新用户密码
                int count = db.Updateable<IFP_UM_USER_INFO>()
                    .SetColumns(x => new IFP_UM_USER_INFO
                    {
                        UsiPassword = user.UsiPassword
                    })
                    .Where(x => x.UsiLoginName == loginname)
                    .ExecuteCommand();

                if (count <= 0)
                {
                    // 如果更新失败（用户不存在），则创建新用户
                    if (string.IsNullOrEmpty(user.Gid.Value))
                    {
                        user.Gid = Guid.NewGuid().ToString("N");
                    }
                    db.Insertable(user).ExecuteCommand();
                }
                else
                {
                    // 如果更新成功，获取更新后的用户信息
                    user = db.Queryable<IFP_UM_USER_INFO>()
                        .Where(x => x.UsiLoginName == loginname)
                        .First();
                }
            }
            return user;

        }

        /// <summary>
        /// iofp单点登录
        /// </summary>
        public bool IofpLogin()
        {
            //从SSOCookie取用户信息--配置nginx代理后在一个域下
            string userInfo = null;
            string msg = null;
            string ssoCookie = WebHelper.GetCookie("SSOCookie");
            LoginParam loginParam = null;
            if (!string.IsNullOrEmpty(ssoCookie))
            {
                loginParam = LoginParam.CreatByIofpUser(ssoCookie);
            }
            //本地cookie
            string session = WebHelper.GetCookie(COM.IFP.Common.StaticConfig.SessionName);
            if (!string.IsNullOrEmpty(session))
            {
                //本地cookie要和SSOCookie相同
                if (loginParam != null)
                {

                    IFP_UM_USER_INFO sessionUser = Newtonsoft.Json.JsonConvert.DeserializeObject<IFP_UM_USER_INFO>(session);
                    if (loginParam.userId == sessionUser.UsiLoginName)
                    {
                        return true;
                    }
                    else
                    {
                        msg = ssoCookie;
                    }
                }
                else
                {
                    return true;
                }
            }
            //另外一种情况，没配置nginx代理，只能从URL上拿参数
            if (string.IsNullOrEmpty(msg))
            {
                userInfo = WebHelper.getRequestParam("SSOParam1");
                string timeSN = WebHelper.getRequestParam("timeSN");
                if (string.IsNullOrEmpty(userInfo) || userInfo == "undefined"
                    || string.IsNullOrEmpty(timeSN) || timeSN == "undefined")
                {
                    return false;
                }
                msg = DESHelper.DES3Decrypt(userInfo);
            }
            if (string.IsNullOrEmpty(msg))
            {
                return false;
            }
            try
            {
                String value = ""; //待写入的cookie值
                //JToken userJson = JToken.Parse(msg);
                loginParam = LoginParam.CreatByIofpUser(msg);
                //loginParam.deptFullName = userJson["deptName"].ToString();
                var vResult = this.GetUserByIdPwd(loginParam.userId, "");
                //var vResult = this.GetUserByIdPwd(loginParam.userId, loginParam.passWord);
                if (vResult == null)//不存在的用户,创建用户及角色信息
                {

                    IFP_UM_USER_INFO user = new IFP_UM_USER_INFO()
                    {
                        //Gid = Guid.NewGuid().ToString("N"),
                        DeptName = loginParam.deptName,
                        DeptFullName = loginParam.deptFullName,
                        UsiLoginName = loginParam.userId,
                        UsiName = loginParam.userName,
                        UsiPassword = loginParam.passWord,
                        UsiSex = String.IsNullOrEmpty(loginParam.sex) ? 0 : int.Parse(loginParam.sex)
                    };
                    user = this.IofpUpdateOrSaveUser(user);

                    user.RoleIds = String.Join(",", loginParam.optList);
                    value = Newtonsoft.Json.JsonConvert.SerializeObject(user);
                }
                else //找到用户直接从库中读取用户信息
                {
                    //密码置为空，不写入session
                    vResult.UsiPassword = new Field<string>();
                    //写入登陆用户的role

                    //vResult.RoleIds = wwwRole.MC_userRoleList(vResult).ToString();
                    vResult.RoleIds = String.Join(",", loginParam.optList);//权限以中心的权限写入
                    value = Newtonsoft.Json.JsonConvert.SerializeObject(vResult);
                }

                WebHelper.WriteCookie(COM.IFP.Common.StaticConfig.SessionName, value);
                WebHelper.WriteSession(COM.IFP.Common.StaticConfig.SessionName, value);
                return true;
            }
            catch (Exception ex)
            {
                LoggerHelper.Error(COM.IFP.Log.ErrorList.E0006, $"远程用户登录=======>>", ex);

                return true;
            }
        }

        public IFP_UM_USER_INFO GetUserByCard(String cardNo)
        {
            using (var db = DB.Create())
            {
                BaseDbHelper<IFP_UM_USER_INFO> dbHelper = new BaseDbHelper<IFP_UM_USER_INFO>(db);
                IFP_UM_USER_INFO searchVO = new IFP_UM_USER_INFO();
                searchVO.CardNo = cardNo;
                List<IFP_UM_USER_INFO> list = dbHelper.QueryEntityList(searchVO);
                return list.SingleOrDefault();
            }
        }

        /// <summary>
        /// 根据用户id获取用户实体对象
        /// </summary>
        /// <param name="objId"></param>
        /// <returns></returns>
        public IFP_UM_USER_INFO QueryUserById(object gid)
        {
            using (var db = DB.Create())
            {
                //BaseDbHelper<IFP_UM_USER_INFO> dbHelper = new BaseDbHelper<IFP_UM_USER_INFO>(db);
                var q = db.Queryable<IFP_UM_USER_INFO>().Where(x => x.Gid == (string)gid).Select(x => x);
                var user = q.First();
                return user;
            }
        }
        ///// <summary>     
        ///// 用户信息分页查询
        ///// </summary>
        ///// <param name="searchVO">查询条件实体</param>
        ///// <returns>分页数据集合</returns>
        //public PageModel<IFP_UM_USER_INFO> UserPageList(IFP_UM_USER_INFO searchVO)
        //{
        //    //using (var db = DB.Create())
        //    //{
        //    //    BaseDbHelper<IFP_UM_USER_INFO> dbHelper = new BaseDbHelper<IFP_UM_USER_INFO>(db);
        //    //    PageModel<IFP_UM_USER_INFO> pageStruct = dbHelper.QueryEntityPageList(searchVO);
        //    //    return pageStruct;
        //    //}
        //    using (var db = DB.Create())
        //    {
        //        BaseDbHelper<IFP_UM_USER_INFO> dbHelper = new BaseDbHelper<IFP_UM_USER_INFO>(db);
        //        PageModel<IFP_UM_USER_INFO> pageStruct = dbHelper.QueryEntityPageList(searchVO);
        //        return pageStruct;
        //    }
        //}
        /// <summary>
        /// 判断当前用户的帐号或者工号是否在数据库存在
        /// </summary>
        /// <param name="user">用户实体对象</param>
        /// <returns>是否存在</returns>
        private bool CheckUserExist(IFP_UM_USER_INFO user)
        {
            using (var db = DB.Create())
            {
                string loginname = user.UsiLoginName.Value;
                string loginnumber = user.UsiNumber.Value;

                var query = from x in db.Queryable<IFP_UM_USER_INFO>()
                            where (x.UsiLoginName == loginname || x.UsiNumber == loginnumber)/* && x.Gid != gid*/
                            select x;
                var l = query.ToList();
                return l.Count() > 0;

            }
        }

        /// <summary>
        /// 保存或者修改用户, 根据GID是否有值
        /// </summary>
        /// <param name="jtoken"></param>
        /// <returns></returns>
        public PFActionResult SaveOrUpdateUser(IFP_UM_USER_INFO entity)
        {
            PFActionResult res = new PFActionResult();
            using (var db = DB.Create())
            {
                Dictionary<string, object> result = new Dictionary<string, object>();

                int n = 0;

                if (string.IsNullOrWhiteSpace(entity.Gid.Value))
                {
                    if (CheckUserExist(entity))
                    {
                        res.success = false;
                        res.msg = "保存失败，原因：登录名称或工号已存在";

                        return res;
                    }

                    entity.Gid = Guid.NewGuid().ToString("N");
                    entity.CreateTime = DateTime.Now;
                    entity.Delt = 0;
                    if (string.IsNullOrWhiteSpace(entity.UsiPassword.Value))
                    {
                        entity.UsiPassword = MD5Helper.Get32MD5(COM.IFP.Common.StaticConfig.DefaultPwd);
                    }
                    n = db.Insertable(entity).ExecuteCommand();
                }
                else
                {
                    n = db.Updateable(entity).ExecuteCommand();
                }
                if (n > 0)
                {
                    res.success = true;
                }
                else
                {
                    res.success = false;
                    res.msg = "插入更新影响0条";
                }
                return res;
            }
        }
        /// <summary>
        /// 删除用户
        /// </summary>
        /// <param name="objId">主键</param>
        /// <returns></returns>
        public PFActionResult DelUser(string objId)
        {

            PFActionResult res = new PFActionResult();
            using (var db = DB.Create())
            {
                int n = db.Deleteable<IFP_UM_USER_INFO>().Where(a => a.Gid == objId).ExecuteCommand();
                if (n > 0)
                {
                    res.success = true;
                }
                else
                {
                    res.success = false;
                    res.msg = "删除影响0条";
                }
                return res;
            }
        }

        /// <summary>
        /// 绑定卡号
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public Dictionary<string, bool> UpdateUserCard(JsonElement obj)
        {
            using (var db = DB.Create())
            {
                Dictionary<string, bool> result = new Dictionary<string, bool>();
                string gid = obj.GetProperty("Gid").GetString();
                string cardNo = obj.GetProperty("CardNo").GetString();

                int count = db.Updateable<IFP_UM_USER_INFO>()
                    .Where(x => x.Gid == gid)
                    .SetColumns(x => x.CardNo == cardNo)
                    .ExecuteCommand();
                result["success"] = count > 0;
                return result;
            }
        }

        /// <summary>
        /// 重置密码
        /// </summary>
        /// <param name="userGid">主键</param>
        /// <returns></returns>
        public PFActionResult ResetPwd(string userGid)
        {
            PFActionResult res = new PFActionResult();
            using (var db = DB.Create())
            {
                string usiPassWord = MD5Helper.Get32MD5(COM.IFP.Common.StaticConfig.DefaultPwd);

                int n = db.Updateable<IFP_UM_USER_INFO>()
                    .Where(x => x.Gid == userGid).SetColumns(x => x.UsiPassword == usiPassWord)
                    .ExecuteCommand();

                if (n > 0)
                {
                    res.success = true;
                }
                else
                {
                    res.success = false;
                    res.msg = "更新影响0条";
                }
                return res;
            }
        }
        /// <summary>
        /// 修改密码
        /// </summary>
        /// <param name="loginParam"></param>
        /// <returns></returns>
        public Dictionary<string, object> ModifyPwd(ORM.IFP.ViewModel.LoginParam loginParam)
        {
            Dictionary<string, object> result = new Dictionary<string, object>();

            // 验证用户原密码
            IFP_UM_USER_INFO user = this.GetUserByIdPwd(loginParam.userId, loginParam.passWord);
            if (user == null)
            {
                result["success"] = false;
                result["data"] = "密码修改失败，原因：密码错误";
                return result;
            }

            using (var db = DB.Create())
            {
                // 更新用户密码
                int rows = db.Updateable<IFP_UM_USER_INFO>()
                    .SetColumns(x => x.UsiPassword == loginParam.newPassWord.ToUpper())
                    .Where(x => x.Gid == user.Gid)
                    .ExecuteCommand();

                result["success"] = true;
                result["data"] = "密码修改成功";
                return result;
            }
        }

        /// <summary>
        /// 远程登录
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="passWord"></param>
        /// <returns></returns>
        public IFP_UM_USER_INFO RemoteLogin(string userId, string passWord)
        {
            if (!string.IsNullOrEmpty(Config.RegCode))
            {
                var loginInfo = new Dictionary<string, object>();
                loginInfo["regCode"] = Config.RegCode; //COM.IFP.Common.Config.regCode;
                loginInfo["userId"] = userId;
                loginInfo["passWord"] = passWord;
                List<string> userInfo = COM.IFP.Client.HttpClientB.PostAndReturn("Setting.UserLogin", Newtonsoft.Json.JsonConvert.SerializeObject(loginInfo));
                if (userInfo != null && userInfo.Count > 0)
                {
                    var userJson = JsonDocument.Parse(userInfo[0]).RootElement;
                    if (userInfo == null || "".Equals(userInfo))
                    {
                        return null;
                    }
                    IFP_UM_USER_INFO vResult = new IFP_UM_USER_INFO()
                    {
                        //Gid = Guid.NewGuid().ToString("N"),
                        DeptName = userJson.TryGetProperty("deptName", out JsonElement deptName) ? deptName.GetString() : userJson.TryGetProperty("deptFullName", out JsonElement deptFullName) ? deptFullName.GetString() : null,
                        DeptFullName = userJson.TryGetProperty("deptFullName", out deptFullName) ? deptFullName.GetString() : null,
                        UsiLoginName = userJson.TryGetProperty("userId", out JsonElement _userId) ? _userId.GetString() : null,
                        UsiName = userJson.TryGetProperty("userId", out JsonElement userName) ? userName.GetString() : null,
                        UsiSex = userJson.TryGetProperty("sex", out JsonElement sex) ? int.Parse("0" + sex.GetString()) : 0
                    };
                    vResult.RoleIds = userJson.TryGetProperty("optList", out JsonElement optList) ? string.Join(".", optList.EnumerateArray().Select(x => x.GetString())) : null;
                    return vResult;
                }
                else
                {
                    return null;
                }
            }
            else
            {
                return null;
            }

        }

        public List<IdTextModel> GetUserIdJobIdText()
        {
            using (var db = DB.Create())
            {
                var q1 = db.Queryable<IFP_UM_USER_INFO>().ToList();
                List<IdTextModel> result = new List<IdTextModel>();
                foreach (var i in q1)
                {
                    result.Add(new IdTextModel
                    {
                        id = i.Gid,
                        text = i.UsiNumber.Value,
                        sxh = i.UsiNumber.Value
                    });
                }
                return result;
            }
        }

        /// <summary>
        /// 初始化管理员
        /// </summary>
        public bool InitAdminUser()
        {
            // 创建管理员用户
            IFP_UM_USER_INFO user = new IFP_UM_USER_INFO
            {
                Gid = "admin",
                UsiName = "管理员",
                UsiLoginName = "admin",
                UsiNumber = "0000",
                UsiPassword = MD5Helper.Get32MD5(COM.IFP.Common.StaticConfig.DefaultPwd),
                CreateTime = DateTime.Now,
                Delt = 0
            };

            // 创建超级管理员角色
            IFP_UM_ROLE role = new IFP_UM_ROLE
            {
                Gid = "role0000",
                RoleName = "超级管理员",
                RoleCode = "0000",
                RoleDoc = "超级权限，系统用户",
                CreateTime = DateTime.Now,
                Delt = 0
            };

            // 创建用户角色关联
            IFP_UM_USER_ROLE user_role = new IFP_UM_USER_ROLE
            {
                Gid = Guid.NewGuid().ToString("N"),
                UsiGuid = "admin",
                RoleGuid = "role0000",
                CreateTime = DateTime.Now,
                Delt = 0
            };

            using (var db = DB.Create())
            {
                try
                {
                    // 开启事务
                    db.BeginTran();

                    // 删除可能存在的旧数据
                    db.Deleteable<IFP_UM_USER_INFO>().Where(a => a.Gid == "admin").ExecuteCommand();
                    db.Deleteable<IFP_UM_ROLE>().Where(a => a.Gid == "role0000").ExecuteCommand();
                    db.Deleteable<IFP_UM_USER_ROLE>()
                        .Where(a => a.UsiGuid == "admin" && a.RoleGuid == "role0000")
                        .ExecuteCommand();

                    // 插入新数据
                    int cnt = 0;
                    cnt += db.Insertable(user).ExecuteCommand();
                    cnt += db.Insertable(role).ExecuteCommand();
                    cnt += db.Insertable(user_role).ExecuteCommand();

                    // 提交事务
                    db.CommitTran();

                    return cnt == 3;
                }
                catch
                {
                    // 发生异常时回滚事务
                    db.RollbackTran();
                    throw;
                }
            }
        }


        public List<string> GetHiddenBtnByUser(string basepage, string pageUrl)
        {
            List<string> result = new List<string>();
            string userGuid = UserCache.GetUserID();
            string userRoles = UserCache.GetRoleIDs();
            userRoles = "role001,role002";
            string[] roles = userRoles.Split(",");

            List<Field<string>> roles1 = roles.Select(x => new Field<string>(x)).ToList();

            Field<string> pageUrl1 = new Field<string>(pageUrl);

            if (UserCache.LoginUserHasSuperRole())// 超级用户设置所有权限
            {
                return result;
            }
            try
            {
                using (SqlSugarClient db = DB.Create())
                {
                    var q1 = db.Queryable<IFP_UM_USER_ROLE>()
                         .Where(x => x.UsiGuid == userGuid || roles1.Contains(x.RoleGuid))
                         .InnerJoin<IFP_UM_ROLE_MENU>((a, b) => a.RoleGuid == b.RoleGuid)
                         .InnerJoin<IFP_UM_MENU>((a, b, c) => b.MenuGuid == c.Gid && c.MenuUrl == basepage)
                         .LeftJoin<IFP_UM_ROLE_BTN>((a, b, c, d) => a.RoleGuid == d.Roleid)
                         .Where((a, b, c, d) => pageUrl.Contains((string)d.Pageurl))
                         .Select((a, b, c, d) => d);

                    string sql = q1.ToSqlString();
                    //LoggerHelper.Info(q1.ToString());
                    List<IFP_UM_ROLE_BTN> roleBtns = q1.ToList();
                    Dictionary<string, int> btnMap = new Dictionary<string, int>();
                    foreach (IFP_UM_ROLE_BTN one in roleBtns)
                    {
                        if (one == null)
                        {
                            continue;
                        }
                        string buttons = one.Buttons.Value;
                        if (string.IsNullOrEmpty(buttons))
                        {
                            continue;
                        }
                        string[] ss = buttons.Split(",");
                        foreach (string b in ss)
                        {
                            if (!string.IsNullOrEmpty(b))
                            {
                                if (!btnMap.ContainsKey(b))
                                {
                                    btnMap.Add(b, 1);
                                }
                                else
                                {
                                    btnMap[b] += 1;
                                }

                            }
                        }
                    }
                    int cnt = roleBtns.Count();
                    foreach (string k in btnMap.Keys)
                    {
                        if (btnMap[k] >= cnt)
                        {
                            result.Add(k);
                        }
                    }
                }
            }
            catch (Exception e)
            {
                LoggerHelper.Error(ErrorList.E9999, e.Message, e);
                return result;
            }
            return result;
        }

        public List<IFP_UM_USER_INFO> GetUserByRoleCode(string roleCode)
        {
            //修改为使用SqlSugar的Queryable API
            //使用类型安全的连接查询替代SQL JOIN
            //使用参数化查询避免SQL注入风险
            using (var db = DB.Create())
            {
                var query = db.Queryable<IFP_UM_USER_INFO>();

                if (!string.IsNullOrEmpty(roleCode))
                {
                    query = db.Queryable<IFP_UM_USER_INFO, IFP_UM_USER_ROLE, IFP_UM_ROLE>(
                            (user, userRole, role) => new JoinQueryInfos(
                                JoinType.Inner, user.Gid == userRole.UsiGuid,
                                JoinType.Inner, userRole.RoleGuid == role.Gid
                            ))
                        .Where((user, userRole, role) => role.RoleCode == roleCode)
                        .Select((user, userRole, role) => user);
                }

                return query.ToList();
            }
        }
    }
}
