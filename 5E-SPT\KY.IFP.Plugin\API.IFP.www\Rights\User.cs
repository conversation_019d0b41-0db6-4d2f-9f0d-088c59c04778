﻿using COM.IFP.Common;
using COM.IFP.SqlSugarN;
using Newtonsoft.Json;
using ORM.IFP.ViewModel;
using ORM.IFP.www.DbModel.UM;
using ORM.IFP.www.DTO;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;

namespace API.IFP.Rights
{
    public class User
    {
        private Lazy<DAL.IFP.Rights.User> user = Entity.Create<DAL.IFP.Rights.User>();

        private Lazy<DAL.IFP.Rights.Role> role = Entity.Create<DAL.IFP.Rights.Role>();

        private delegate void test<T1, T2>(out T1 out1, out T2 out2);

        public void tttt(out string out1, out string out2)
        {
            out1 = default;
            out2 = default;
        }
        /// <summary>
        /// iofp单点登录
        /// </summary>
        public string iofpLogin()
        {
            return user.Value.IofpLogin() ? "SUCCESS" : "ERROR";
        }
        /// <summary>
        /// iofp单点登出
        /// </summary>
        /// <returns></returns>
        public string iofpLogOut()
        {
            WebHelper.RemoveCookie(StaticConfig.SessionName);
            return "success";

        }

        /// <summary>
        /// 登出
        /// </summary>
        /// <param name="jt">空</param>
        /// <returns></returns>
        public LoginResult LoginOut(JsonElement jt)
        {
            WebHelper.RemoveCookie(StaticConfig.SessionName);
            LoginResult login = new LoginResult();
            login.code = "0";
            login.url = Config.LocalSystem.GetProperty("LoginHtml").GetString();
            login.msg = "";
            return login;

        }
        /// <summary>
        /// 用户账号登录
        /// </summary>
        /// <param name="json">{"loginType":"local/remote/card/cardCheck","userId":"用户名，loginType为local/remote/card时需要","passWord":"密码，登录方式为local/remote时需要,"cardNo":"卡号","who":"此次是第几用户登录"}</param>
        /// <returns>{"code":"登录成功或失败的错误代码","msg":"登录成功或失败的错误消息","url":"登录成功后的跳转页面"}</returns>
        public object Login(JsonElement json)
        {
            try
            {

                var info = json.GetValue<(string loginType, string userId, string passWord, string cardNo, string who)>(x => new { x.loginType, x.userId, x.passWord, x.cardNo, x.who });
                IFP_UM_USER_INFO vResult = null;
                string errorMsg = "用户不存在或密码错误";
                switch (info.loginType)
                {
                    case "local":
                    default:
                        #region 本地登录
                        vResult = user.Value.GetUserByIdPwd(info.userId, info.passWord);
                        if (vResult == null)
                        {
                            errorMsg = "用户不存在或密码错误";
                        }
                        break;
                    #endregion
                    case "remote":
                        #region 远程登录
                        vResult = user.Value.RemoteLogin(info.userId, info.passWord);
                        if (vResult == null)
                        {
                            errorMsg = "用户不存在或密码错误";
                        }
                        break;
                    #endregion
                    case "cardCheck":
                    case "card":
                        #region 刷卡登录
                        vResult = user.Value.GetUserByCard(info.cardNo);
                        if (vResult == null)
                        {
                            errorMsg = "该卡号未绑定用户";
                        }
                        break;
                        #endregion
                }
                if (vResult == null)
                {
                    return new
                    {
                        code = "2",
                        msg = errorMsg
                    };
                }
                else
                {
                    if (string.IsNullOrEmpty(vResult.RoleIds))
                    {
                        vResult.RoleIds = role.Value.UserRoleList(vResult.Gid.Value);
                    }
                    //密码置为空，不写入session
                    vResult.UsiPassword = new Field<string>();
                    //var userJsonStr = Newtonsoft.Json.JsonConvert.SerializeObject(vResult);
                    var userJsonStr = System.Text.Json.JsonSerializer.Serialize(vResult);
                    var login = new
                    {
                        code = "0",
                        url = Config.LocalSystem.GetProperty("MainHtml").GetString(),
                        msg = "",
                        user = userJsonStr
                    };
                    //无参是旧的使用方式只有单用户登录，
                    //第二人登录，只验证不写cookie、session，
                    if (info.who != null &&
                        info.who == "2") return login;
                    if (!"cardCheck".Equals(info.loginType))
                    {
                        WebHelper.WriteSession(StaticConfig.SessionName, vResult);
                        WebHelper.WriteCookie(StaticConfig.SessionName, userJsonStr);
                    }
                    return login;
                }
            }
            catch (Exception ex)
            {
                return new
                {
                    code = "2",
                    msg = ex.ToString()
                };
            };
        }

        public Dictionary<string, object> Welcome(JsonElement json)
        {
            Dictionary<string, object> result = new Dictionary<string, object>();

            string message = string.Empty;
            Dictionary<string, string> dicAllRole = new Dictionary<string, string>() {
                {"role0000","超级管理员" },
                {"role0001","编辑用户" },
                {"role0002","查看用户" },
                {"role0003","操作员2" },
                {"role0004","操作员3" },
            };

            List<IFP_UM_ROLE> roleList = role.Value.RoleList();

            if (roleList.Count > 0)
            {
                dicAllRole.Clear();
                foreach (var it in roleList)
                {
                    dicAllRole.Add(it.Gid.Value, it.RoleName.Value);
                }
            }
            //var x = Caches.Values;
            //byte[] bCookie = (byte[])Caches.GetData("kjsoftUserCookie");
            //string cookie= System.Text.Encoding.Default.GetString(bCookie);
            string cookie = WebHelper.GetCookie(StaticConfig.SessionName);

            if (string.IsNullOrEmpty(cookie))
            {
                message = "您暂无已分配的权限，可以使用基本功能";
                result["message"] = message;
                return result;
            }
            JsonElement jsonElement = JsonDocument.Parse(cookie).RootElement;
            string roleIds = string.Empty;
            if (jsonElement.TryGetProperty("RoleIds", out JsonElement jsonElement2))
            {
                roleIds = jsonElement2.GetString();
            }
            if (string.IsNullOrEmpty(roleIds))
            {
                message = "您暂无已分配的权限，可以使用基本功能";
                result["message"] = message;
            }
            List<string> roleIDList = roleIds.Split(",").ToList();
            if (roleIDList == null || roleIDList.Count == 0)
            {
                message = "您暂无已分配的权限，可以使用基本功能";
                result["message"] = message;
                return result;
            }
            message += "您拥有";
            //用字典防止2角色同菜单重复
            Dictionary<string, string> menuIDName = new Dictionary<string, string>();
            List<string> menuNameList = new List<string>();

            if (dicAllRole.ContainsKey(roleIDList[0]))
            {
                string roleGid = roleIDList[0];
                message += dicAllRole[roleGid];
                List<IFP_UM_MENU> allMenues = role.Value.RoleMenu(roleGid);
                foreach (var it in allMenues)
                {
                    menuIDName[it.Gid.Value] = it.MenuName.Value;
                }
            }
            for (int i = 1; i < roleIDList.Count; ++i)
            {
                string roleGid = roleIDList[i];
                if (dicAllRole.ContainsKey(roleGid))
                {
                    message += "、" + dicAllRole[roleGid];
                    List<IFP_UM_MENU> allMenues = role.Value.RoleMenu(roleGid);
                    foreach (var it in allMenues)
                    {
                        menuIDName[it.Gid.Value] = it.MenuName.Value;
                    }
                }
            }
            foreach (var it in menuIDName.Values)
            {
                menuNameList.Add(it);
            }
            if (roleIDList.Contains("role0000"))
            {
                message += "角色权限，可以使用全部功能";
            }
            else
            {
                message += "角色权限，可使用这些额外功能";
            }
            result["message"] = message;
            result["menuNameList"] = menuNameList;
            return result;
        }

        public object GetUserList(JsonElement json)
        {
            //LoginParam entity = JsonConvert.DeserializeObject<LoginParam>(obj.ToString());
            //IFP_UM_USER_INFO param = JsonConvert.DeserializeObject<IFP_UM_USER_INFO>(json.ToString());
            //string str = json.ToString();

            //var filter1 = json.GetValue<IList<IFP_UM_USER_INFO>>("filter");

            IList<IFP_UM_USER_INFO> filter = null;
            var tmp1 = new JsonElement();
            if (json.TryGetProperty("filter", out tmp1) == true)
                filter = json.GetValue<IList<IFP_UM_USER_INFO>>("filter");

            PageModel paging = null;
            var tmp = new JsonElement();
            if (json.TryGetProperty("paging", out tmp) == true)
                paging = json.GetValue<PageModel>("paging");


            return user.Value.GetUserList(filter, paging);
        }

        /// <summary>
        /// 请求服务器时间
        /// </summary>
        /// <param name="json"></param>
        /// <returns></returns>
        public object ProgramStartTime()
        {
            return user.Value.ProgramStartTime();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public Dictionary<string, object> ProductSetting()
        {
            JsonElement obj = Config.LocalSystem;
            var nameString = obj.GetProperty("Product").GetProperty("Name").GetString();

            var homepageString = obj.GetProperty("Product").GetProperty("HomePage").GetString();
            var mainNameString = obj.GetProperty("Product").GetProperty("MainName").GetString();
            var picPathString = obj.GetProperty("Product").GetProperty("LoginPic").GetString();
            var OrgName = obj.GetProperty("Product").GetProperty("OrgName").GetString();

            Dictionary<string, object> map = new Dictionary<string, object>();
            map["Name"] = nameString;
            map["HomePage"] = homepageString;
            map["MainName"] = mainNameString;
            map["LoginPic"] = picPathString;
            map["OrgName"] = OrgName;

            JsonElement jObj_dataCenter = Config.DataCenter; ;
            if (jObj_dataCenter.ValueKind != JsonValueKind.Undefined)
            {
                //获取好相应的鉴权密钥
                map["AuthKey"] = jObj_dataCenter.GetProperty("authKey").GetString();
            }
            else
            {
                map["AuthKey"] = "Mw4qdSAmYn+Jlys5bpn/eCMA0KMQCQ9qmdd1WX0Nqo5GMYaRQBuXmg==";//默认鉴权码
            }
            return map;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public Dictionary<string, object> SystemSetting()
        {
            JsonElement obj = Config.LocalSystem;
            var timeoutMode = obj.GetProperty("TimeoutMode").GetBoolean();
            var logoutTime = obj.GetProperty("LogoutTime").GetInt32();
            Dictionary<string, object> map = new Dictionary<string, object>();

            map["TimeoutMode"] = timeoutMode;
            map["LogoutTime"] = logoutTime;
            return map;
        }
        public IFP_UM_USER_INFO QueryUserById(JsonElement json)
        {
            //IFP_UM_USER_INFO entity = JsonConvert.DeserializeObject<IFP_UM_USER_INFO>(obj.ToString());
            var gid = json.GetValue<string>("Gid");

            return user.Value.QueryUserById(gid);
        }

        public object UserList(JsonElement obj)
        {
            //IFP_UM_USER_INFO entity = JsonConvert.DeserializeObject<IFP_UM_USER_INFO>(obj.ToString());
            //PageModel<IFP_UM_USER_INFO> model = new PageModel<IFP_UM_USER_INFO>();
            //return user.Value.UserPageList(entity);
            return GetUserList(obj);
        }

        public object SaveOrUpdateUser(JsonElement obj)
        {
            //IFP_UM_USER_INFO entity = JsonConvert.DeserializeObject<IFP_UM_USER_INFO>(obj.ToString());
            IFP_UM_USER_INFO entity = obj.GetValue<IFP_UM_USER_INFO>();

            return user.Value.SaveOrUpdateUser(entity);
        }

        public object DelUser(JsonElement obj)
        {
            return user.Value.DelUser(obj.GetProperty("gid").GetString());
        }

        public Dictionary<string, bool> UpdateUserCard(JsonElement obj)
        {
            return user.Value.UpdateUserCard(obj);
        }

        public object ResetPwd(JsonElement obj)
        {
            return user.Value.ResetPwd(obj.GetProperty("gid").GetString());
        }
        public Dictionary<string, object> ModifyPwd(JsonElement obj)
        {
            // 创建序列化设置并添加Newtonsoft的JSON转换器
            var settings = new JsonSerializerSettings
            {
                Converters = new List<JsonConverter> { new NewtonsoftFieldConverter() }
            };

            // 使用自定义设置进行反序列化
            LoginParam entity = JsonConvert.DeserializeObject<LoginParam>(obj.ToString(), settings);
            return user.Value.ModifyPwd(entity);
        }

        /// <summary>
        /// 用户gid和用户名的映射关系
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public List<IdTextModel> GetUserIdTextList(JsonElement obj)
        {
            //LoginParam entity = JsonConvert.DeserializeObject<LoginParam>(obj.ToString());
            return user.Value.GetUserIdTextList();
        }
        /// <summary>
        /// daiabin用户gid和用户工号的映射关系
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public List<IdTextModel> GetUserIdJobIdText(JsonElement obj)
        {
            return user.Value.GetUserIdJobIdText();
        }
        /// <summary>
        /// 验证帐号密码
        /// </summary>
        /// <param name="user">{UsiLoginName:'',UsiPassWord:'前端MD5加密'}</param>
        /// <returns></returns>
        public PFActionResult CheckUser(JsonElement json)
        {
            IFP_UM_USER_INFO param = json.GetValue<IFP_UM_USER_INFO>();
            PFActionResult result = new PFActionResult();
            IFP_UM_USER_INFO vResult = user.Value.GetUserByIdPwd(param.UsiLoginName.Value, param.UsiPassword.Value);
            if (vResult == null)
            {
                result.success = false;
                result.msg = "用户不存在或密码错误";
            }
            else
            {
                result.success = true;
                result.msg = "验证成功";
            }
            return result;
        }

        /// <summary>
        /// 初始化管理员
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public Dictionary<string, bool> InitAdminUser()
        {
            Dictionary<string, bool> map = new Dictionary<string, bool>();
            bool v = user.Value.InitAdminUser();
            map.Add("success", v);
            return map;
        }

        public List<string> GetHiddenBtnByUser(JsonElement obj)
        {
            string pageurl = obj.GetValue<string>("pageurl");
            string basepage = obj.GetValue<string>("basepage");
            return user.Value.GetHiddenBtnByUser(basepage, pageurl);
        }

        public List<IdTextModel> GetUserByRoleCode(JsonElement json)
        {
            string rolecode = json.GetValue<string>("code");
            List<IFP_UM_USER_INFO> list = user.Value.GetUserByRoleCode(rolecode);
            List<IdTextModel> reList = new List<IdTextModel>();
            foreach (IFP_UM_USER_INFO one in list)
            {
                reList.Add(new IdTextModel("USER", one.Gid, null, one.UsiName.Value, one.Delt == 1 ? 1 : 0));
            }
            return reList;
        }
    }
}
