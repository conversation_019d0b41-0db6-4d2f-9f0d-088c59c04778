﻿using COM.IFP.Common;
using COM.IFP.Log;
using COM.IFP.SqlSugarN;
using ORM.IFP.www.DbModel.UM;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using PageModel = COM.IFP.Common.PageModel;

namespace DAL.IFP.Rights
{
    /// <summary>
    /// 系统按钮管理数据访问层
    /// </summary>
    public class SystemButton
    {
        /// <summary>
        /// 系统按钮查询（统一查询接口）
        /// </summary>
        /// <param name="filter">过滤条件</param>
        /// <param name="paging">分页信息</param>
        /// <returns>按钮列表</returns>
        public PageModel<IFP_SYSTEM_BUTTONS> SelectButton(IList<IFP_SYSTEM_BUTTONS> filter, PageModel paging)
        {
            using (var db = DB.Create())
            {
                var wheres = filter.ToArray();
                var result = db.Queryable<IFP_SYSTEM_BUTTONS>()
                    .Where(x => x.Delt == 0) // 只查询未删除的记录
                    .Where(wheres)
                    .Order(wheres)
                    .Fetch(paging);
                return result;
            }
        }

        /// <summary>
        /// 根据页面URL获取按钮列表
        /// </summary>
        /// <param name="pageUrl">页面URL</param>
        /// <returns>按钮列表</returns>
        public List<IFP_SYSTEM_BUTTONS> GetButtonsByPage(string pageUrl)
        {
            using (var db = DB.Create())
            {
                return db.Queryable<IFP_SYSTEM_BUTTONS>()
                    .Where(x => x.PageUrl == pageUrl && x.Delt == 0)
                    .OrderBy(x => x.Sort)
                    .ToList();
            }
        }

        /// <summary>
        /// 根据ID获取按钮详情
        /// </summary>
        /// <param name="gid">按钮ID</param>
        /// <returns>按钮信息</returns>
        public IFP_SYSTEM_BUTTONS GetButtonById(string gid)
        {
            using (var db = DB.Create())
            {
                return db.Queryable<IFP_SYSTEM_BUTTONS>()
                    .Where(x => x.Gid == gid && x.Delt == 0)
                    .First();
            }
        }

        /// <summary>
        /// 保存系统按钮（新增或修改）
        /// </summary>
        /// <param name="button">按钮信息</param>
        /// <returns>操作结果</returns>
        public PFActionResult SaveButton(IFP_SYSTEM_BUTTONS button)
        {
            using (var db = DB.Create())
            {
                try
                {
                    // 判断是新增还是修改
                    bool isAdd = string.IsNullOrWhiteSpace(button.Gid.Value);

                    if (isAdd)
                    {
                        return AddButtonInternal(db, button);
                    }
                    else
                    {
                        return UpdateButtonInternal(db, button);
                    }
                }
                catch (Exception ex)
                {
                    string operation = string.IsNullOrWhiteSpace(button.Gid.Value) ? "新增" : "修改";
                    LoggerHelper.Error(ErrorList.E9999, $"{operation}系统按钮失败: {ex.Message}", ex);
                    return new PFActionResult
                    {
                        success = false,
                        msg = $"{operation}按钮失败：{ex.Message}"
                    };
                }
            }
        }

        /// <summary>
        /// 内部新增按钮方法
        /// </summary>
        /// <param name="db">数据库连接</param>
        /// <param name="button">按钮信息</param>
        /// <returns>操作结果</returns>
        private PFActionResult AddButtonInternal(SqlSugarClient db, IFP_SYSTEM_BUTTONS button)
        {
            // 检查按钮ID是否已存在
            var existingButton = db.Queryable<IFP_SYSTEM_BUTTONS>()
                .Where(x => x.ButtonId == button.ButtonId && x.PageUrl == button.PageUrl && x.Delt == 0)
                .First();

            if (existingButton != null)
            {
                return new PFActionResult
                {
                    success = false,
                    msg = $"按钮ID '{button.ButtonId.Value}' 在页面 '{button.PageUrl.Value}' 中已存在"
                };
            }

            // 生成新的GID
            button.Gid = GenerateButtonGid();
            button.CreateTime = DateTime.Now;
            button.Creator = UserCache.GetUserID();
            button.IsEnabled = 1;
            button.Delt = 0;

            // 如果未指定排序，则使用最大排序 + 1
            if (button.Sort.Value <= 0)
            {
                var maxSort = db.Queryable<IFP_SYSTEM_BUTTONS>()
                    .Where(x => x.PageUrl == button.PageUrl && x.Delt == 0)
                    .Max(x => x.Sort.Value);
                button.Sort = maxSort + 1;
            }

            int rows = db.Insertable(button).ExecuteCommand();

            return new PFActionResult
            {
                success = rows > 0,
                msg = rows > 0 ? "按钮新增成功" : "按钮新增失败",
                data = new { Gid = button.Gid.Value, Operation = "Add" }
            };
        }

        /// <summary>
        /// 内部修改按钮方法
        /// </summary>
        /// <param name="db">数据库连接</param>
        /// <param name="button">按钮信息</param>
        /// <returns>操作结果</returns>
        private PFActionResult UpdateButtonInternal(SqlSugarClient db, IFP_SYSTEM_BUTTONS button)
        {
            // 检查按钮是否存在
            var existingButton = db.Queryable<IFP_SYSTEM_BUTTONS>()
                .Where(x => x.Gid == button.Gid && x.Delt == 0)
                .First();

            if (existingButton == null)
            {
                return new PFActionResult
                {
                    success = false,
                    msg = "按钮不存在或已被删除"
                };
            }

            // 检查按钮ID是否与其他按钮冲突
            var conflictButton = db.Queryable<IFP_SYSTEM_BUTTONS>()
                .Where(x => x.ButtonId == button.ButtonId && x.PageUrl == button.PageUrl
                           && x.Gid != button.Gid && x.Delt == 0)
                .First();

            if (conflictButton != null)
            {
                return new PFActionResult
                {
                    success = false,
                    msg = $"按钮ID '{button.ButtonId.Value}' 在页面 '{button.PageUrl.Value}' 中已存在"
                };
            }

            // 只更新指定的业务字段，不更新系统字段
            int rows = db.Updateable<IFP_SYSTEM_BUTTONS>()
                .SetColumns(x => new IFP_SYSTEM_BUTTONS
                {
                    ButtonId = button.ButtonId,
                    ButtonName = button.ButtonName,
                    PageUrl = button.PageUrl,
                    PageName = button.PageName,
                    ButtonGroup = button.ButtonGroup,
                    ButtonType = button.ButtonType,
                    Icon = button.Icon,
                    Description = button.Description,
                    Sort = button.Sort,
                    Remark = button.Remark,
                    UpdateTime = DateTime.Now,
                    Updater = UserCache.GetUserID()
                })
                .Where(x => x.Gid == button.Gid)
                .ExecuteCommand();

            return new PFActionResult
            {
                success = rows > 0,
                msg = rows > 0 ? "按钮修改成功" : "按钮修改失败",
                data = new { Gid = button.Gid.Value, Operation = "Update" }
            };
        }

        /// <summary>
        /// 删除系统按钮（软删除）
        /// </summary>
        /// <param name="gid">按钮ID</param>
        /// <returns>操作结果</returns>
        public PFActionResult DeleteButton(string gid)
        {
            using (var db = DB.Create())
            {
                try
                {
                    // 检查按钮是否存在
                    var existingButton = db.Queryable<IFP_SYSTEM_BUTTONS>()
                        .Where(x => x.Gid == gid && x.Delt == 0)
                        .First();

                    if (existingButton == null)
                    {
                        return new PFActionResult
                        {
                            success = false,
                            msg = "按钮不存在或已被删除"
                        };
                    }

                    // 软删除
                    int rows = db.Updateable<IFP_SYSTEM_BUTTONS>()
                        .SetColumns(x => new IFP_SYSTEM_BUTTONS
                        {
                            Delt = 1,
                            UpdateTime = DateTime.Now,
                            Updater = UserCache.GetUserID()
                        })
                        .Where(x => x.Gid == gid)
                        .ExecuteCommand();

                    return new PFActionResult
                    {
                        success = rows > 0,
                        msg = rows > 0 ? "按钮删除成功" : "按钮删除失败"
                    };
                }
                catch (Exception ex)
                {
                    LoggerHelper.Error(ErrorList.E9999, $"删除系统按钮失败: {ex.Message}", ex);
                    return new PFActionResult
                    {
                        success = false,
                        msg = $"删除按钮失败：{ex.Message}"
                    };
                }
            }
        }

        /// <summary>
        /// 批量删除系统按钮
        /// </summary>
        /// <param name="gids">按钮ID列表</param>
        /// <returns>操作结果</returns>
        public PFActionResult BatchDeleteButtons(List<string> gids)
        {
            using (var db = DB.Create())
            {
                try
                {
                    db.BeginTran();

                    int successCount = 0;
                    foreach (var gid in gids)
                    {
                        var result = DeleteButton(gid);
                        if (result.success)
                        {
                            successCount++;
                        }
                    }

                    db.CommitTran();

                    return new PFActionResult
                    {
                        success = successCount > 0,
                        msg = $"批量删除完成，成功删除 {successCount}/{gids.Count} 个按钮"
                    };
                }
                catch (Exception ex)
                {
                    db.RollbackTran();
                    LoggerHelper.Error(ErrorList.E9999, $"批量删除系统按钮失败: {ex.Message}", ex);
                    return new PFActionResult
                    {
                        success = false,
                        msg = $"批量删除按钮失败：{ex.Message}"
                    };
                }
            }
        }

        /// <summary>
        /// 批量导入按钮
        /// </summary>
        /// <param name="buttons">按钮列表</param>
        /// <returns>操作结果</returns>
        public PFActionResult BatchImportButtons(List<IFP_SYSTEM_BUTTONS> buttons)
        {
            using (var db = DB.Create())
            {
                try
                {
                    db.BeginTran();

                    int successCount = 0;
                    var errorMessages = new List<string>();

                    foreach (var button in buttons)
                    {
                        //var result = AddButton(button);
                        var result = AddButtonInternal(db, button);
                        if (result.success)
                        {
                            successCount++;
                        }
                        else
                        {
                            errorMessages.Add($"按钮 '{button.ButtonId.Value}': {result.msg}");
                        }
                    }

                    db.CommitTran();

                    var message = $"批量导入完成，成功导入 {successCount}/{buttons.Count} 个按钮";
                    if (errorMessages.Count > 0)
                    {
                        message += $"\n失败原因：{string.Join("; ", errorMessages)}";
                    }

                    return new PFActionResult
                    {
                        success = successCount > 0,
                        msg = message
                    };
                }
                catch (Exception ex)
                {
                    db.RollbackTran();
                    LoggerHelper.Error(ErrorList.E9999, $"批量导入系统按钮失败: {ex.Message}", ex);
                    return new PFActionResult
                    {
                        success = false,
                        msg = $"批量导入按钮失败：{ex.Message}"
                    };
                }
            }
        }

        /// <summary>
        /// 获取所有页面列表
        /// </summary>
        /// <returns>页面列表</returns>
        public List<object> GetPageList()
        {
            using (var db = DB.Create())
            {
                // 从按钮表中获取页面信息
                var buttonPages = db.Queryable<IFP_SYSTEM_BUTTONS>()
                    .Where(x => x.Delt == 0)
                    .GroupBy(x => new { x.PageUrl, x.PageName })
                    .Select(x => new {
                        PageUrl = x.PageUrl,
                        PageName = x.PageName,
                        ButtonCount = SqlFunc.AggregateCount(x.Gid)
                    })
                    .ToList();

                // 从菜单表中获取页面信息
                var menuPages = db.Queryable<IFP_UM_MENU>()
                    .Where(x => x.Delt == 0 && !string.IsNullOrEmpty(x.MenuUrl.Value))
                    .Select(x => new {
                        PageUrl = x.MenuUrl.Value,
                        PageName = x.MenuName.Value,
                        ButtonCount = 0
                    })
                    .ToList();

                // 合并去重
                var allPages = buttonPages.Cast<object>().ToList();
                foreach (var menuPage in menuPages)
                {
                    if (!buttonPages.Any(bp => bp.PageUrl == menuPage.PageUrl))
                    {
                        allPages.Add(menuPage);
                    }
                }

                return allPages.OrderBy(x => ((dynamic)x).PageUrl).ToList();
            }
        }

        /// <summary>
        /// 获取按钮分组列表
        /// </summary>
        /// <returns>分组列表</returns>
        public List<string> GetButtonGroups()
        {
            using (var db = DB.Create())
            {
                return db.Queryable<IFP_SYSTEM_BUTTONS>()
                    .Where(x => x.Delt == 0 && !string.IsNullOrEmpty(x.ButtonGroup.Value))
                    .GroupBy(x => x.ButtonGroup)
                    .Select(x => x.ButtonGroup.Value)
                    .ToList();
            }
        }

        /// <summary>
        /// 启用/禁用按钮
        /// </summary>
        /// <param name="gid">按钮ID</param>
        /// <param name="isEnabled">是否启用</param>
        /// <returns>操作结果</returns>
        public PFActionResult ToggleButtonStatus(string gid, bool isEnabled)
        {
            using (var db = DB.Create())
            {
                try
                {
                    // 检查按钮是否存在
                    var existingButton = db.Queryable<IFP_SYSTEM_BUTTONS>()
                        .Where(x => x.Gid == gid && x.Delt == 0)
                        .First();

                    if (existingButton == null)
                    {
                        return new PFActionResult
                        {
                            success = false,
                            msg = "按钮不存在或已被删除"
                        };
                    }

                    int rows = db.Updateable<IFP_SYSTEM_BUTTONS>()
                        .SetColumns(x => new IFP_SYSTEM_BUTTONS
                        {
                            IsEnabled = isEnabled ? 1 : 0,
                            UpdateTime = DateTime.Now,
                            Updater = UserCache.GetUserID()
                        })
                        .Where(x => x.Gid == gid)
                        .ExecuteCommand();

                    return new PFActionResult
                    {
                        success = rows > 0,
                        msg = rows > 0 ? $"按钮{(isEnabled ? "启用" : "禁用")}成功" : $"按钮{(isEnabled ? "启用" : "禁用")}失败"
                    };
                }
                catch (Exception ex)
                {
                    LoggerHelper.Error(ErrorList.E9999, $"切换按钮状态失败: {ex.Message}", ex);
                    return new PFActionResult
                    {
                        success = false,
                        msg = $"切换按钮状态失败：{ex.Message}"
                    };
                }
            }
        }

        /// <summary>
        /// 复制按钮到其他页面
        /// </summary>
        /// <param name="sourceGid">源按钮ID</param>
        /// <param name="targetPageUrl">目标页面URL</param>
        /// <param name="targetPageName">目标页面名称</param>
        /// <returns>操作结果</returns>
        public PFActionResult CopyButtonToPage(string sourceGid, string targetPageUrl, string targetPageName)
        {
            using (var db = DB.Create())
            {
                try
                {
                    // 获取源按钮信息
                    var sourceButton = db.Queryable<IFP_SYSTEM_BUTTONS>()
                        .Where(x => x.Gid == sourceGid && x.Delt == 0)
                        .First();

                    if (sourceButton == null)
                    {
                        return new PFActionResult
                        {
                            success = false,
                            msg = "源按钮不存在或已被删除"
                        };
                    }

                    // 检查目标页面是否已存在相同的按钮ID
                    var existingButton = db.Queryable<IFP_SYSTEM_BUTTONS>()
                        .Where(x => x.ButtonId == sourceButton.ButtonId && x.PageUrl == targetPageUrl && x.Delt == 0)
                        .First();

                    if (existingButton != null)
                    {
                        return new PFActionResult
                        {
                            success = false,
                            msg = $"目标页面中已存在按钮ID '{sourceButton.ButtonId.Value}'"
                        };
                    }

                    // 创建新按钮
                    var newButton = new IFP_SYSTEM_BUTTONS
                    {
                        Gid = GenerateButtonGid(),
                        ButtonId = sourceButton.ButtonId,
                        ButtonName = sourceButton.ButtonName,
                        PageUrl = targetPageUrl,
                        PageName = targetPageName,
                        ButtonGroup = sourceButton.ButtonGroup,
                        ButtonType = sourceButton.ButtonType,
                        Icon = sourceButton.Icon,
                        Description = sourceButton.Description,
                        Sort = GetNextSortForPage(targetPageUrl),
                        IsEnabled = 1,
                        CreateTime = DateTime.Now,
                        Creator = UserCache.GetUserID(),
                        Delt = 0
                    };

                    int rows = db.Insertable(newButton).ExecuteCommand();

                    return new PFActionResult
                    {
                        success = rows > 0,
                        msg = rows > 0 ? "按钮复制成功" : "按钮复制失败",
                        data = newButton.Gid.Value
                    };
                }
                catch (Exception ex)
                {
                    LoggerHelper.Error(ErrorList.E9999, $"复制按钮失败: {ex.Message}", ex);
                    return new PFActionResult
                    {
                        success = false,
                        msg = $"复制按钮失败：{ex.Message}"
                    };
                }
            }
        }

        /// <summary>
        /// 获取按钮统计信息
        /// </summary>
        /// <param name="pageUrl">页面URL，为空则统计所有</param>
        /// <returns>统计信息</returns>
        public object GetButtonStatistics(string pageUrl = "")
        {
            using (var db = DB.Create())
            {
                var query = db.Queryable<IFP_SYSTEM_BUTTONS>()
                    .Where(x => x.Delt == 0);

                if (!string.IsNullOrWhiteSpace(pageUrl))
                {
                    query = query.Where(x => x.PageUrl == pageUrl);
                }

                var totalButtons = query.Count();
                var enabledButtons = query.Where(x => x.IsEnabled == 1).Count();

                var pageStats = db.Queryable<IFP_SYSTEM_BUTTONS>()
                    .Where(x => (int)x.Delt == 0)
                    .GroupBy(x => new { x.PageUrl, x.PageName })
                    .Select(x => new {
                        PageUrl = (string)x.PageUrl,
                        PageName = (string)x.PageName,
                        ButtonCount = SqlFunc.AggregateCount(x.Gid),
                        EnabledCount = SqlFunc.AggregateSum(SqlFunc.IIF(x.IsEnabled == 1, 1, 0))
                    })
                    .ToList();

                var groupStats = db.Queryable<IFP_SYSTEM_BUTTONS>()
                    .Where(x => (int)x.Delt == 0 && !string.IsNullOrEmpty((string)x.ButtonGroup))
                    .GroupBy(x => x.ButtonGroup)
                    .Select(x => new {
                        GroupName = (string)x.ButtonGroup,
                        ButtonCount = SqlFunc.AggregateCount(x.Gid)
                    })
                    .ToList();

                return new
                {
                    TotalButtons = totalButtons,
                    EnabledButtons = enabledButtons,
                    DisabledButtons = totalButtons - enabledButtons,
                    PageCount = pageStats.Count,
                    GroupCount = groupStats.Count,
                    PageStatistics = pageStats,
                    GroupStatistics = groupStats
                };
            }
        }

        /// <summary>
        /// 获取页面选项
        /// </summary>
        /// <returns>页面选项列表</returns>
        public List<object> GetPageOptions()
        {
            using (var db = DB.Create())
            {
                // 从按钮表中获取页面信息
                var buttonPages = db.Queryable<IFP_SYSTEM_BUTTONS>()
                    .Where(x => (int)x.Delt == 0)
                    .GroupBy(x => new { x.PageUrl, x.PageName })
                    .Select(x => new {
                        Value = (string)x.PageUrl,
                        Label = (string)x.PageName,
                        ButtonCount = SqlFunc.AggregateCount(x.Gid)
                    })
                    .ToList();

                // 从菜单表中获取页面信息
                var menuPages = db.Queryable<IFP_UM_MENU>()
                    .Where(x => (int)x.Delt == 0 && !string.IsNullOrEmpty((string)x.MenuUrl))
                    .Select(x => new {
                        Value = (string)x.MenuUrl,
                        Label = (string)x.MenuName,
                        ButtonCount = 0
                    })
                    .ToList();

                // 使用字典进行去重合并
                var pageDict = new Dictionary<string, object>();

                // 处理按钮页面数据
                foreach (var page in buttonPages)
                {
                    var normalizedUrl = NormalizePageUrl(page.Value);
                    var key = normalizedUrl;

                    if (!pageDict.ContainsKey(key))
                    {
                        pageDict[key] = new
                        {
                            Value = normalizedUrl,
                            Label = page.Label,
                            ButtonCount = page.ButtonCount
                        };
                    }
                    else
                    {
                        // 如果已存在，更新按钮数量（取较大值）
                        var existing = (dynamic)pageDict[key];
                        if (page.ButtonCount > existing.ButtonCount)
                        {
                            pageDict[key] = new
                            {
                                Value = normalizedUrl,
                                Label = page.Label,
                                ButtonCount = page.ButtonCount
                            };
                        }
                    }
                }

                // 处理菜单页面数据（只添加不存在的）
                foreach (var menuPage in menuPages)
                {
                    var normalizedUrl = NormalizePageUrl(menuPage.Value);
                    var key = normalizedUrl;

                    if (!pageDict.ContainsKey(key))
                    {
                        pageDict[key] = new
                        {
                            Value = normalizedUrl,
                            Label = menuPage.Label,
                            ButtonCount = menuPage.ButtonCount
                        };
                    }
                }

                // 转换为列表并排序（按 Label 排序，有按钮的页面优先）
                return pageDict.Values
                    .OrderByDescending(x => ((dynamic)x).ButtonCount) // 先按按钮数量倒序
                    .ThenBy(x => ((dynamic)x).Label) // 再按标签排序
                    .ToList();
            }
        }

        /// <summary>
        /// 标准化页面URL，移除 /index 后缀
        /// </summary>
        /// <param name="url"></param>
        /// <returns></returns>
        private string NormalizePageUrl(string url)
        {
            if (string.IsNullOrEmpty(url))
                return url;

            // 移除 /index 后缀
            if (url.EndsWith("/index", StringComparison.OrdinalIgnoreCase))
            {
                return url.Substring(0, url.Length - 6);
            }

            return url;
        }

        /// <summary>
        /// 获取分组选项
        /// </summary>
        /// <returns>分组选项列表</returns>
        public List<object> GetGroupOptions()
        {
            using (var db = DB.Create())
            {
                var groups = db.Queryable<IFP_SYSTEM_BUTTONS>()
                    .Where(x => (int)x.Delt == 0 && !string.IsNullOrEmpty((string)x.ButtonGroup))
                    .GroupBy(x => x.ButtonGroup)
                    .Select(x => new {
                        Value = (string)x.ButtonGroup,
                        Label = (string)x.ButtonGroup,
                        ButtonCount = SqlFunc.AggregateCount(x.Gid)
                    })
                    .ToList();

                return groups.Cast<object>().OrderBy(x => ((dynamic)x).Value).ToList();
            }
        }

        /// <summary>
        /// 获取按钮类型选项
        /// </summary>
        /// <returns>类型选项列表</returns>
        public List<object> GetTypeOptions()
        {
            return new List<object>
            {
                new { Value = "primary", Label = "主要", Color = "#409EFF" },
                new { Value = "success", Label = "成功", Color = "#67C23A" },
                new { Value = "info", Label = "信息", Color = "#909399" },
                new { Value = "warning", Label = "警告", Color = "#E6A23C" },
                new { Value = "danger", Label = "危险", Color = "#F56C6C" },
                new { Value = "default", Label = "默认", Color = "#C0C4CC" }
            };
        }

        #region 私有方法

        /// <summary>
        /// 生成按钮GID
        /// </summary>
        /// <returns>新的按钮GID</returns>
        private string GenerateButtonGid()
        {
            using (var db = DB.Create())
            {
                // 获取当前最大的按钮编号
                var maxtempGid = db.Queryable<IFP_SYSTEM_BUTTONS>()
                                .Where(x => SqlFunc.StartsWith((string)x.Gid, "BTN") && SqlFunc.Length(x.Gid) == 6)
                                .OrderBy(x => x.Gid, OrderByType.Desc)
                                .Select(x => x.Gid)
                                .First();
                var maxGid = maxtempGid.Value;

                int nextNumber = 1;
                if (!string.IsNullOrEmpty(maxGid))
                {
                    var numberPart = maxGid.Substring(3); // 去掉"BTN"前缀
                    if (int.TryParse(numberPart, out int currentNumber))
                    {
                        nextNumber = currentNumber + 1;
                    }
                }

                return $"BTN{nextNumber:D3}";
            }
        }

        /// <summary>
        /// 获取页面下一个排序号
        /// </summary>
        /// <param name="pageUrl">页面URL</param>
        /// <returns>排序号</returns>
        private int GetNextSortForPage(string pageUrl)
        {
            using (var db = DB.Create())
            {
                var maxSort = db.Queryable<IFP_SYSTEM_BUTTONS>()
                    .Where(x => x.PageUrl == pageUrl && x.Delt == 0)
                    .Max(x => x.Sort.Value);

                return maxSort + 1;
            }
        }

        #endregion
    }
}