using COM.IFP.Log;
using COM.IFP.SqlSugarN;
using DAL.SPT.Service.CardReaders;
using ORM.SPT;
using ORM.SPT.www;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace DAL.SPT.Service
{
    /// <summary>
    /// 任务执行器
    /// </summary>
    public class TaskExecutor
    {
        #region 核心状态管理

        /// <summary>
        /// 全局锁，确保所有关键操作的线程安全
        /// </summary>
        private static readonly object _globalLock = new object();
        /// <summary>
        /// 当前正在执行的任务（静态，全局唯一）
        /// </summary>
        private static SPT_COMMAND _currentExecuteTask = null;
        /// <summary>
        /// 是否是重启程序
        /// </summary>
        private static bool _needRecoveryCheck = true; 

        /// <summary>
        /// 任务状态点位映射，用于监听任务状态变化
        /// </summary>
        private static readonly Dictionary<string, (int Status, string Message)> StatusPointMapping = new Dictionary<string, (int Status, string Message)>
        {
            { Constant.CSWCZT, (2, "任务正常完成") },
            { Constant.CSCSZT, (3, "任务超时") },
            { Constant.LJCWZT, (6, "路径错误") },
            { Constant.TXZD, (8, "通讯中断") },
            { Constant.BNJSZT, (0, "目标站暂时不能接收") }
        };

        /// <summary>
        /// 构造函数
        /// </summary>
        public TaskExecutor()
        {

        }
        #endregion

        #region 任务执行流程图
        /*
        开始 → 检查点位存储器 → 获取全局锁 → 创建数据库连接
        ↓
        【步骤1：恢复任务 只有程序第一次启动时处理】
        检查内存中是否有当前任务
        ↓ 无
        查询数据库中STATUS=1的任务
        ↓ 有运行中任务
        恢复到内存 → 记录日志 → 返回（等待下次循环）
        ↓ 无运行中任务
        继续下一步

        【步骤2：状态检查】
        当前任务存在 且 STATUS=1？
        ↓ 是
        检查系统站点位状态变化
        ↓ 有状态变化
        更新任务状态到数据库 → 清理内存任务 → 重置点位 → 返回
        ↓ 无状态变化
        返回（任务继续执行）

        【步骤3：清理完成任务】
        当前任务存在 且 STATUS≠1？
        ↓ 是
        记录清理日志 → 清空内存任务
        ↓
        继续下一步

        【步骤4：执行新任务】
        内存中无当前任务？
        ↓ 是
        查询STATUS=0的待执行任务（按优先级排序）
        ↓ 有待执行任务
        暂停读卡器 → 检查执行条件
        ↓ 条件满足
        写入任务点位 → 更新任务状态为1 → 赋值到内存 → 记录启动日志
        ↓ 条件不满足
        标记任务失败（STATUS=6） → 记录错误日志
        ↓ 无待执行任务
        启动读卡器（如果已暂停）
        ↓
        结束本次循环
         */
        #endregion

        #region 核心任务处理方法

        /// <summary>
        /// 检查并执行下一个待执行的任务
        /// </summary>
        public void CheckAndExecuteNextTask()
        {  
            // 如果存储器未初始化，直接返回
            if (Global.DataMemory == null)
            {
                LogUtility.ToError("点位存储器未初始化", 2);
                return;
            }

            lock (_globalLock)
            {
                using var db = DB.Create();
                
                // 1. 恢复可能丢失的执行中任务 避免重复检查 只做一次恢复
                if (_currentExecuteTask == null && _needRecoveryCheck)
                {
                    var runningTask = db.Queryable<SPT_COMMAND>().Where(t => t.STATUS == 1).First();
                    _needRecoveryCheck = false;
                    if (runningTask != null)
                    {
                        _currentExecuteTask = runningTask;
                        LogUtility.ToNotice($"恢复执行中任务：样品={_currentExecuteTask.SAMPLE.Value}", 2);
                        return;
                    }
                }

                // 2. 检查当前任务状态
                if (_currentExecuteTask != null && _currentExecuteTask.STATUS == 1)
                {
                    // 进行系统站点位状态检查
                    CheckAndUpdateTaskStatus(_currentExecuteTask, db);
                    return; // 任务仍在执行中，直接返回
                }

                // 3. 清理已完成任务
                if(_currentExecuteTask != null && _currentExecuteTask.STATUS != 1)
                {
                    LogUtility.ToNotice($"清理已完成任务：样品={_currentExecuteTask.SAMPLE.Value}, 状态={_currentExecuteTask.STATUS}", 2);
                    _currentExecuteTask = null;
                }

                // 4. 查找并执行新任务
                if (_currentExecuteTask == null)
                {
                    var nextTask = db.Queryable<SPT_COMMAND>()
                   .Where(t => t.STATUS == 0) // 只查询未开始的任务
                   .OrderByDescending(t => t.MANUAL) // 手动任务优先
                   .OrderByDescending(t => t.PRIORITY) // 优先级高的优先
                   .OrderBy(t => t.CREATE_TIME) // 创建时间早的优先
                   .First();

                    if (nextTask != null)
                    {
                        // 有任务则暂停读卡器
                        CardReader.Pause();

                        // 一次性完整设置状态检查
                        if (CheckTaskExecutionConditions(nextTask))
                        {
                            try
                            {
                                // 下法任务
                                WriteTaskPointValues(nextTask);

                                // 任务下发成功，更新状态
                                UpdateTaskStatus(nextTask, 1, db, setStartTime: true);

                                // 成功后再赋值
                                _currentExecuteTask = nextTask;

                                string taskType = nextTask.MANUAL == 1 ? "手动" : "自动";
                                LogUtility.ToNotice($"启动{taskType}任务：样品={nextTask.SAMPLE.Value}", 2);
                            }
                            catch (Exception ex)
                            {
                                LogUtility.ToError($"下发任务失败：样品={nextTask.SAMPLE.Value}", 2, ex);
                            }
                        }
                        else
                        {
                            // 条件不满足，标记任务失败
                            UpdateTaskStatus(nextTask, 6, db, setEndTime: true); // 其它错误
                            LogUtility.ToError($"任务执行条件不满足，标记为失败：样品={nextTask.SAMPLE.Value}", 2);
                        }
                    }
                    else
                    {
                        // 没有任务则启动读卡器
                        if (Global.CARD_READER_PAUSED)
                        {
                            CardReader.Reset();
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 检查并更新任务状态
        /// </summary>
        /// <param name="task">任务对象</param>
        /// <param name="db">数据库连接</param>
        private void CheckAndUpdateTaskStatus(SPT_COMMAND task, ISqlSugarClient db)
        {
            // 检查任务状态点位
            foreach (var point in StatusPointMapping)
            {
                // 不能接受状态
                if (point.Key == Constant.BNJSZT)
                {
                    if (Global.CheckPointValue("01" + point.Key, true))
                    {
                        // 如果是不能接收状态 回滚任务状态
                        LogUtility.ToNotice($"目标站暂时不能接收，任务({_currentExecuteTask.SAMPLE.Value})恢复到未执行状态", 2);

                        // 恢复任务到初始状态
                        UpdateTaskStatus(_currentExecuteTask, 0, db);
                        _currentExecuteTask = null;

                        // 重置不能接收状态
                        Global.WritePointValue("01" + point.Key, false);
                        return; // 处理完成，直接返回
                    }
                }
                else
                {
                    // 其他情况 根据点位值更新任务状态
                    if (Global.CheckPointValue("01" + point.Key, true))
                    {
                        // 更新任务状态
                        UpdateTaskStatus(task, point.Value.Status, db, setEndTime: true);
                        _currentExecuteTask = null;
                        LogUtility.ToNotice($"任务状态更新：样品={task.SAMPLE.Value}, 状态={point.Value.Message}", 2);

                        // 重置点位状态 根据实际是否需要重置点位
                        // WritePointValue("01" + point.Key, false);
                        return; // 处理完成，直接返回
                    }
                }
            }
        }

        /// <summary>
        /// 更新任务状态的统一方法
        /// </summary>
        /// <param name="task">要更新的任务</param>
        /// <param name="newStatus">新状态</param>
        /// <param name="db">数据库连接</param>
        /// <param name="setEndTime">是否设置结束时间</param>
        /// <param name="setStartTime">是否设置开始时间</param>
        private void UpdateTaskStatus(SPT_COMMAND task, int newStatus, ISqlSugarClient db, bool setEndTime = false, bool setStartTime = false)
        {
            task.STATUS = newStatus;

            if (setStartTime)
            {
                task.START_TIME = DateTime.Now;
            }

            if (setEndTime)
            {
                task.END_TIME = DateTime.Now;
            }

            db.Updateable(task).ExecuteCommand();
        }

        /// <summary>
        /// 设备状态检查
        /// </summary>
        /// <param name="task">要检查的任务</param>
        /// <returns>是否可以执行</returns>
        private bool CheckTaskExecutionConditions(SPT_COMMAND task)
        {
            try
            {
                string originStationId = task.ORIGIN.Value;
                string targetStationId = task.TARGET.Value;
                
                // 检查PLC连接状态
                if (!Global.PlcIsConnected)
                    return false;

                // 设备就绪状态
                if (!Global.CheckPointValue("01" + Constant.ZBJXXH, true))
                    return false;

                // 自动运行
                if (!Global.CheckPointValue("01" + Constant.ZDYXML, true))
                    return false;

                // 2. 通讯状态检查
                if (Global.CheckPointValue(originStationId + Constant.TXZD, true))
                {
                    LogUtility.ToNotice($"起点站{originStationId}通讯中断，无法下发任务({task.SAMPLE.Value})", 2);
                    return false;
                }

                if (Global.CheckPointValue(targetStationId + Constant.TXZD, true))
                {
                    LogUtility.ToNotice($"目标站{targetStationId}通讯中断，无法下发任务({task.SAMPLE.Value})", 2);
                    return false;
                }
                // 3. 起点站物料检查（非存查样柜）
                if (originStationId[0] != '6' && !Global.CheckPointValue(originStationId + Constant.YPJC, true))
                {
                    LogUtility.ToNotice($"起点站{originStationId}没有检测到样瓶，无法下发任务({task.SAMPLE.Value})", 2);
                    return false;
                }

                // 4. 目标站状态检查
                if (Global.CheckPointValue(targetStationId + Constant.YPJC, true))
                {
                    LogUtility.ToNotice($"目标站{targetStationId}已存在样瓶，无法下发任务({task.SAMPLE.Value})", 2);
                    return false;
                }

                // 5. 目标站就绪检查
                if (targetStationId[0] == '6')
                {
                    // 存查样柜瓶子规格检查
                    return task.BOT.Value switch
                    {
                        0 => Global.CheckPointValue(targetStationId + Constant.XPJD, true) ||
                             LogAndReturnFalse($"目标站{targetStationId}小瓶未准备就绪，无法下发任务({task.SAMPLE.Value})"),
                        1 => Global.CheckPointValue(targetStationId + Constant.DPJD, true) ||
                             LogAndReturnFalse($"目标站{targetStationId}大瓶未准备就绪，无法下发任务({task.SAMPLE.Value})"),
                        2 => Global.CheckPointValue(targetStationId + Constant.ZPJD, true) ||
                             LogAndReturnFalse($"目标站{targetStationId}中瓶未准备就绪，无法下发任务({task.SAMPLE.Value})"),
                        _ => LogAndReturnFalse($"未知的瓶子规格：{task.BOT.Value}")
                    };
                }
                else
                {
                    // 普通站点就绪检查
                    if (!Global.CheckPointValue(targetStationId + Constant.ZBJXXH, true))
                    {
                        LogUtility.ToNotice($"目标站{targetStationId}未准备就绪，无法下发任务({task.SAMPLE.Value})", 2);
                        return false;
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                LogUtility.ToError($"检查任务条件异常：{task.SAMPLE.Value}", 2, ex);
                return false;
            }
        }
        
        /// <summary>
        /// 记录日志并返回false的辅助方法
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <returns>始终返回false</returns>
        private static bool LogAndReturnFalse(string message)
        {
            LogUtility.ToNotice(message, 2);
            return false;
        }

        /// <summary>
        /// 写入任务点位值
        /// </summary>
        private void WriteTaskPointValues(SPT_COMMAND task)
        {
            string originStationId = task.ORIGIN.Value;
            string targetStationId = task.TARGET.Value;
            bool isBigBottle = task.BOT.Value == 1; // 大瓶子

            // 1. 先将传送完成状态置为false
            if (!Global.WritePointValue("01" + Constant.CSWCZT, false))
            {
                string errorMsg = "更改传送完成状态失败";
                LogUtility.ToError(errorMsg, 2);
                throw new Exception(errorMsg);
            }
            
            // 2. 先将吸气完成状态置为false
            if (!Global.WritePointValue("01" + Constant.XQWCZT, false))
            {
                string errorMsg = "更改吸气完成状态失败";
                LogUtility.ToError(errorMsg, 2);
                throw new Exception(errorMsg);
            }
            
            // 3. 写入发送站点命令
            if (!Global.WritePointValue("01" + Constant.FSZDML, originStationId))
            {
                string errorMsg = "写入发送站点命令失败";
                LogUtility.ToError(errorMsg, 2);
                throw new Exception(errorMsg);
            }
            
            // 4. 写入接收站点命令
            if (!Global.WritePointValue("01" + Constant.JSZDML, targetStationId))
            {
                string errorMsg = "写入接收站点命令失败";
                LogUtility.ToError(errorMsg, 2);
                throw new Exception(errorMsg);
            }
            //5.写入瓶类型
            if (!Global.WritePointValue("01" + Constant.SFDPML, isBigBottle))
            {
                string errorMsg = "写入瓶类型失败";
                LogUtility.ToError(errorMsg, 2);
                throw new Exception(errorMsg);
            }
            
            /*
            // 5. 写入传输标识命令 这里根据点位描述 这个值下位机无需处理，根据实际考虑 现版本是否需要
            
            if (!WritePointValue("01" + Constant.CSBSML, task.GID.Value))
            {
                string errorMsg = "写入传输标识命令失败";
                LogUtility.ToError(errorMsg, 2);
                return false;
            }
            LogUtility.ToNotice($"写入传输标识：GID={task.GID.Value}", 2);
            

            // 6. 如果是手动任务，写入手动命令   没有这个点位 结合实际是否需要这个
            if (task.MANUAL == 1 && !WritePointValue("01" + Constant.SDML, 1))
            {
                string errorMsg = "写入手动命令失败";
                LogUtility.ToError(errorMsg, 2);
                throw new Exception(errorMsg);
            }
            */
        }

        #endregion

    }
}