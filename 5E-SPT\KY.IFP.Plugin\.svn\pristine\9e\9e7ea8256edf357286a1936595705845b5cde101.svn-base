﻿using COM.IFP.Common;
using COM.IFP.Common.Json;
using COM.IFP.Log;
using ORM.IFP.www.DbModel.UM;
using System;
using System.Text.Json;

namespace DAL.IFP.Rights
{
    public class UserCache
    {
        /// <summary>
        /// 获取当前登陆用户对象，如果使用一体化管控做代理且没有用户同步则可能并不指实际登录的用户cookie
        /// </summary>
        public static IFP_UM_USER_INFO LoginUser
        {
            get
            {
                try
                {
                    var loginJson = WebHelper.GetCookie(StaticConfig.SessionName);
                    if (string.IsNullOrEmpty(loginJson))
                    {
                        var obj = WebHelper.GetSession(StaticConfig.SessionName);
                        if (obj == null)
                        {
                            return null;
                        }
                        else
                        {
                            loginJson = System.Text.Encoding.Default.GetString((byte[])obj);
                        }

                    }
                    //var user = JsonConvert.DeserializeObject<IFP_UM_USER_INFO>(loginJson);

                    var json = JsonDocument.Parse(loginJson).RootElement;
                    var user = json.GetValue<IFP_UM_USER_INFO>();
                    return user;
                }
                catch (Exception e)
                {
                    LoggerHelper.Error("获取用户对象出错", e);
                    return null;

                }

            }

        }
        /// <summary>
        /// 登陆用户是否有超级权限
        /// </summary>
        /// <returns></returns>
        public static bool LoginUserHasSuperRole()
        {
            return LoginUser != null && !string.IsNullOrEmpty(LoginUser.RoleIds) && LoginUser.RoleIds.IndexOf(StaticConfig.SuperRoleId) >= 0;
        }
        /// <summary>
        /// 如果使用一体化管控做代理且没有用户同步则可能并不指实际登录的用户cookie，此时先从一体化cookie找用户名，找不到再从自身cookie找
        /// </summary>
        /// <returns>cookie无用户则返回空串或空</returns>
        public static string GetUserName()
        {
            try
            {
                //配置了一体化的cookie名则处理
                if (Config.LocalSystem.TryGetProperty("ythCookie", out JsonElement cookieName1Jele))
                {
                    string cookieName1 = "172_210_3_16kjsoftUserCookie";
                    cookieName1 = cookieName1Jele.ToString();
                    //一体化的cookie名非空则处理
                    if (!string.IsNullOrEmpty(cookieName1))
                    {
                        var cookie1 = WebHelper.GetCookie(cookieName1);
                        if (!string.IsNullOrEmpty(cookie1))
                        {
                            JsonElement jele = JsonDocument.Parse(cookie1, JsonDocumentOptionsUtil.General).RootElement;
                            if (jele.ValueKind != JsonValueKind.Undefined && jele.TryGetProperty("userName", out JsonElement userNameJele))
                            {
                                string userName = userNameJele.ToString();
                                return userName;
                            }
                            return string.Empty;
                        }
                    }
                }
                //否则就是管控本身的cookie

                string cookieName2 = StaticConfig.SessionName;
                var cookie2 = WebHelper.GetCookie(cookieName2);

                IFP_UM_USER_INFO userObj;

                if (!string.IsNullOrEmpty(cookie2))
                {
                    var json1 = JsonDocument.Parse(cookie2, JsonDocumentOptionsUtil.General).RootElement;
                    userObj = json1.GetValue<IFP_UM_USER_INFO>();
                    return userObj.UsiName.Value;
                }
                var session2 = WebHelper.GetSession(cookieName2);
                if (session2 == null)
                {
                    return string.Empty;
                }
                var loginJson = System.Text.Encoding.Default.GetString((byte[])session2);

                var json = JsonDocument.Parse(loginJson, JsonDocumentOptionsUtil.General).RootElement;
                var user = json.GetValue<IFP_UM_USER_INFO>();
                return user.UsiName.Value;
            }
            catch (Exception e)
            {
                LoggerHelper.Error("获取用户名失败", e);
                return string.Empty;
            }
        }

        /// <summary>
        /// 只支持，本系统用户对象的ID
        /// </summary>
        /// <returns>本系统用户对象的ID</returns>
        public static string GetUserID()
        {
            try
            {
                var loginJson = WebHelper.GetCookie(StaticConfig.SessionName);
                if (string.IsNullOrEmpty(loginJson))
                {
                    var obj = WebHelper.GetSession(StaticConfig.SessionName);
                    if (obj == null)
                    {
                        return string.Empty;
                    }
                    else
                    {
                        loginJson = System.Text.Encoding.Default.GetString((byte[])obj);
                    }

                }
                var json = JsonDocument.Parse(loginJson).RootElement;
                var user = json.GetValue<IFP_UM_USER_INFO>();
                return user.Gid.Value;
            }
            catch (Exception e)
            {
                LoggerHelper.Error("获取用户ID失败", e);

                return string.Empty;
            }

        }

        /// <summary>
        /// cookie里存了RoleIds
        /// </summary>
        /// <returns></returns>
        public static string GetRoleIDs()
        {
            try
            {
                var loginJson = WebHelper.GetCookie(StaticConfig.SessionName);
                if (string.IsNullOrEmpty(loginJson))
                {
                    var obj = WebHelper.GetSession(StaticConfig.SessionName);
                    if (obj == null)
                    {
                        return string.Empty;
                    }
                    else
                    {
                        loginJson = System.Text.Encoding.Default.GetString((byte[])obj);
                    }

                }
                var json = JsonDocument.Parse(loginJson).RootElement;
                var user = json.GetValue<IFP_UM_USER_INFO>();
                return user.RoleIds;
            }
            catch (Exception e)
            {
                LoggerHelper.Error("获取角色失败", e);

                return string.Empty;
            }
        }
    }
}
