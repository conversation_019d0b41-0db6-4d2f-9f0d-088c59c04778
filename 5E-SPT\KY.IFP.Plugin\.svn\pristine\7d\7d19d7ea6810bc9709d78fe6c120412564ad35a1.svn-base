﻿using Newtonsoft.Json;

namespace ORM.IFP.ViewModel
{
    /// <summary>
    /// 用户登录的参数实体类
    /// </summary>
    public class LoginParam
    {

        /// <summary>
        /// 用户id
        /// </summary>
        public string userId { set; get; }

        /// <summary>
        ///  ID卡号
        /// </summary>
        public string CardNo { set; get; }

        /// <summary>
        ///  密码,前端进行BASE64加密后
        /// </summary>
        public string passWord { set; get; }

        /// <summary>
        /// 记住密码
        /// </summary>
        public bool remamber { set; get; }

        /// <summary>
        /// 用户名称
        /// </summary>
        public string userName { set; get; }

        /// <summary>
        /// 性别 0男 1女
        /// </summary>
        public string sex { set; get; }
        /// <summary>
        /// 部门名称
        /// </summary>
        public string deptName { set; get; }
        /// <summary>
        /// 部门名称（全）
        /// </summary>
        public string deptFullName { set; get; }
        /// <summary>
        /// 操作权限列表
        /// </summary>
        public string[] optList { set; get; }
        /// <summary>
        /// 新密码,用于重设密码时候使用
        /// </summary>
        public string newPassWord { set; get; }

        /// <summary>
        /// 根据iofp单点登录信息创建LoginParam
        /// </summary>
        /// <param name="userInfo"></param>
        /// <returns></returns>
        public static LoginParam CreatByIofpUser(string userInfo)
        {
            if (string.IsNullOrWhiteSpace(userInfo))
            {
                return null;
            }
            LoginParam loginParam = JsonConvert.DeserializeObject<LoginParam>(userInfo);
            loginParam.sex = loginParam.sex == "男" ? "0" : "1";
            return loginParam;
        }
    }


}
