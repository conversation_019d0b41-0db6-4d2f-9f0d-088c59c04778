﻿using COM.IFP.Common;
using COM.IFP.Common.Secure;
using COM.IFP.Log;
using COM.IFP.SqlSugarN;
using Newtonsoft.Json;
using ORM.IFP.www.DbModel.UM;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using PageModel = COM.IFP.Common.PageModel;
using StaticConfig = COM.IFP.Common.StaticConfig;

namespace DAL.IFP.Rights
{
    /// <summary>
    /// 用户扩展信息数据访问层 - 新项目专用
    /// 处理用户扩展信息的数据库操作，包括JSON扩展字段的序列化/反序列化
    /// </summary>
    public class UserExt
    {
        /// <summary>
        /// 查询用户扩展信息
        /// </summary>
        /// <param name="filter">查询条件</param>
        /// <param name="paging">分页信息</param>
        /// <returns>分页查询结果</returns>
        public PageModel<IFP_UM_USER_INFO> SelectUserExt(IList<IFP_UM_USER_INFO> filter, PageModel paging)
        {
            using (var db = DB.Create())
            {
                try
                {
                    var wheres = filter?.ToArray();
                    var query = db.Queryable<IFP_UM_USER_INFO>()
                                 .Where(x => x.Delt == 0); // 只查询未删除的用户

                    if (wheres != null && wheres.Length > 0)
                    {
                        query = query.Where(wheres).Order(wheres);
                    }

                    var result = query.Fetch(paging);

                    // 处理扩展信息的反序列化
                    if (result.rows != null)
                    {
                        foreach (var user in result.rows)
                        {
                            DeserializeExtInfo(user);
                        }
                    }

                    LoggerHelper.Info($"查询用户扩展信息完成，返回 {result.rows?.Count ?? 0} 条记录");
                    return result;
                }
                catch (Exception ex)
                {
                    LoggerHelper.Error(ErrorList.E9999, $"查询用户扩展信息失败: {ex.Message}", ex);
                    throw;
                }
            }
        }

        /// <summary>
        /// 保存用户扩展信息（新增或更新）
        /// </summary>
        /// <param name="userInfo">用户扩展信息</param>
        /// <returns>操作结果</returns>
        public PFActionResult SaveUserExt(IFP_UM_USER_INFO userInfo)
        {
            using (var db = DB.Create())
            {
                try
                {
                    db.BeginTran();

                    // 序列化扩展信息
                    SerializeExtInfo(userInfo);

                    bool isNew = string.IsNullOrWhiteSpace(userInfo.Gid.Value);

                    if (isNew)
                    {
                        // 新增用户
                        userInfo.Gid = Guid.NewGuid().ToString("N");
                        userInfo.CreateTime = DateTime.Now;
                        userInfo.Delt = 0;

                        // 设置默认密码
                        if (string.IsNullOrWhiteSpace(userInfo.UsiPassword.Value))
                        {
                            userInfo.UsiPassword = MD5Helper.Get32MD5(StaticConfig.DefaultPwd);
                        }

                        // 验证必要字段
                        var validationResult = ValidateUserInfo(userInfo, true);
                        if (!validationResult.success)
                        {
                            db.RollbackTran();
                            return validationResult;
                        }

                        // 检查登录名和工号是否已存在
                        var existsResult = CheckUserExists(db, userInfo);
                        if (!existsResult.success)
                        {
                            db.RollbackTran();
                            return existsResult;
                        }

                        int insertRows = db.Insertable(userInfo).ExecuteCommand();
                        if (insertRows <= 0)
                        {
                            db.RollbackTran();
                            return new PFActionResult
                            {
                                success = false,
                                msg = "新增用户失败"
                            };
                        }

                        LoggerHelper.Info($"新增用户成功: {userInfo.UsiName.Value}({userInfo.Gid.Value})");
                    }
                    else
                    {
                        // 更新用户
                        var existingUser = db.Queryable<IFP_UM_USER_INFO>()
                                            .Where(x => x.Gid == userInfo.Gid && x.Delt == 0)
                                            .First();

                        if (existingUser == null)
                        {
                            db.RollbackTran();
                            return new PFActionResult
                            {
                                success = false,
                                msg = "用户不存在或已被删除"
                            };
                        }

                        // 保留创建信息
                        userInfo.CreateTime = existingUser.CreateTime;
                        userInfo.Delt = existingUser.Delt;

                        // 如果密码为空，保持原密码
                        if (string.IsNullOrWhiteSpace(userInfo.UsiPassword.Value))
                        {
                            userInfo.UsiPassword = existingUser.UsiPassword;
                        }

                        // 验证必要字段
                        var validationResult = ValidateUserInfo(userInfo, false);
                        if (!validationResult.success)
                        {
                            db.RollbackTran();
                            return validationResult;
                        }

                        int updateRows = db.Updateable(userInfo).ExecuteCommand();
                        if (updateRows <= 0)
                        {
                            db.RollbackTran();
                            return new PFActionResult
                            {
                                success = false,
                                msg = "更新用户失败"
                            };
                        }

                        LoggerHelper.Info($"更新用户成功: {userInfo.UsiName.Value}({userInfo.Gid.Value})");
                    }

                    db.CommitTran();

                    // 返回处理后的用户信息（反序列化扩展信息用于显示）
                    DeserializeExtInfo(userInfo);

                    return new PFActionResult
                    {
                        success = true,
                        msg = isNew ? "新增用户成功" : "更新用户成功",
                        data = userInfo
                    };
                }
                catch (Exception ex)
                {
                    db.RollbackTran();
                    LoggerHelper.Error(ErrorList.E9999, $"保存用户扩展信息失败: {ex.Message}", ex);
                    return new PFActionResult
                    {
                        success = false,
                        msg = $"保存用户失败：{ex.Message}"
                    };
                }
            }
        }

        /// <summary>
        /// 根据用户ID获取扩展信息
        /// </summary>
        /// <param name="userGid">用户ID</param>
        /// <returns>用户扩展信息</returns>
        public PFActionResult GetUserExtByGid(string userGid)
        {
            using (var db = DB.Create())
            {
                try
                {
                    var user = db.Queryable<IFP_UM_USER_INFO>()
                             .Where(x => x.Gid == userGid && x.Delt == 0)
                             .First();

                    if (user == null)
                    {
                        return new PFActionResult
                        {
                            success = false,
                            msg = "用户不存在或已被删除"
                        };
                    }

                    // 反序列化扩展信息
                    DeserializeExtInfo(user);

                    // 清空密码信息
                    user.UsiPassword = new Field<string>();

                    return new PFActionResult
                    {
                        success = true,
                        msg = "获取用户信息成功",
                        data = user
                    };
                }
                catch (Exception ex)
                {
                    LoggerHelper.Error(ErrorList.E9999, $"获取用户信息失败: {ex.Message}", ex);
                    return new PFActionResult
                    {
                        success = false,
                        msg = $"获取用户信息失败：{ex.Message}"
                    };
                }
            }
        }

        /// <summary>
        /// 删除用户（软删除）
        /// </summary>
        /// <param name="userGid">用户ID</param>
        /// <returns>删除结果</returns>
        public PFActionResult DeleteUserExt(string userGid)
        {
            using (var db = DB.Create())
            {
                try
                {
                    db.BeginTran();

                    // 检查用户是否存在
                    var user = db.Queryable<IFP_UM_USER_INFO>()
                             .Where(x => x.Gid == userGid && x.Delt == 0)
                             .First();

                    if (user == null)
                    {
                        db.RollbackTran();
                        return new PFActionResult
                        {
                            success = false,
                            msg = "用户不存在或已被删除"
                        };
                    }

                    // 软删除用户
                    int rows = db.Updateable<IFP_UM_USER_INFO>()
                              .SetColumns(x => x.Delt == 1)
                              .Where(x => x.Gid == userGid)
                              .ExecuteCommand();

                    if (rows <= 0)
                    {
                        db.RollbackTran();
                        return new PFActionResult
                        {
                            success = false,
                            msg = "删除用户失败"
                        };
                    }

                    // 同时软删除用户角色关联
                    db.Updateable<IFP_UM_USER_ROLE>()
                      .SetColumns(x => x.Delt == 1)
                      .Where(x => x.UsiGuid == userGid)
                      .ExecuteCommand();

                    db.CommitTran();

                    LoggerHelper.Info($"删除用户成功: {user.UsiName.Value}({userGid})");

                    return new PFActionResult
                    {
                        success = true,
                        msg = "删除用户成功"
                    };
                }
                catch (Exception ex)
                {
                    db.RollbackTran();
                    LoggerHelper.Error(ErrorList.E9999, $"删除用户失败: {ex.Message}", ex);
                    return new PFActionResult
                    {
                        success = false,
                        msg = $"删除用户失败：{ex.Message}"
                    };
                }
            }
        }

        /// <summary>
        /// 仅更新用户扩展信息字段
        /// </summary>
        /// <param name="userGid">用户ID</param>
        /// <param name="extInfo">扩展信息</param>
        /// <returns>更新结果</returns>
        public PFActionResult UpdateUserExtInfo(string userGid, UserExtInfo extInfo)
        {
            using (var db = DB.Create())
            {
                try
                {
                    // 检查用户是否存在
                    var user = db.Queryable<IFP_UM_USER_INFO>()
                             .Where(x => x.Gid == userGid && x.Delt == 0)
                             .First();

                    if (user == null)
                    {
                        return new PFActionResult
                        {
                            success = false,
                            msg = "用户不存在或已被删除"
                        };
                    }

                    // 序列化扩展信息
                    string extInfoJson = extInfo != null ? JsonConvert.SerializeObject(extInfo) : null;

                    // 仅更新扩展信息字段
                    int rows = db.Updateable<IFP_UM_USER_INFO>()
                              .SetColumns(x => x.ExtInfo == extInfoJson)
                              .Where(x => x.Gid == userGid)
                              .ExecuteCommand();

                    if (rows <= 0)
                    {
                        return new PFActionResult
                        {
                            success = false,
                            msg = "更新用户扩展信息失败"
                        };
                    }

                    LoggerHelper.Info($"更新用户扩展信息成功: {user.UsiName.Value}({userGid})");

                    return new PFActionResult
                    {
                        success = true,
                        msg = "更新用户扩展信息成功",
                        data = extInfo
                    };
                }
                catch (Exception ex)
                {
                    LoggerHelper.Error(ErrorList.E9999, $"更新用户扩展信息失败: {ex.Message}", ex);
                    return new PFActionResult
                    {
                        success = false,
                        msg = $"更新用户扩展信息失败：{ex.Message}"
                    };
                }
            }
        }

        /// <summary>
        /// 批量导入用户扩展信息
        /// </summary>
        /// <param name="userList">用户列表</param>
        /// <returns>导入结果</returns>
        public PFActionResult BatchImportUserExt(List<IFP_UM_USER_INFO> userList)
        {
            using (var db = DB.Create())
            {
                try
                {
                    db.BeginTran();

                    int successCount = 0;
                    int failureCount = 0;
                    var errorMessages = new List<string>();

                    foreach (var user in userList)
                    {
                        try
                        {
                            // 处理单个用户
                            if (string.IsNullOrWhiteSpace(user.Gid.Value))
                            {
                                user.Gid = Guid.NewGuid().ToString("N");
                            }

                            user.CreateTime = DateTime.Now;
                            user.Delt = 0;

                            // 设置默认密码
                            if (string.IsNullOrWhiteSpace(user.UsiPassword.Value))
                            {
                                user.UsiPassword = MD5Helper.Get32MD5(StaticConfig.DefaultPwd);
                            }

                            // 序列化扩展信息
                            SerializeExtInfo(user);

                            // 检查是否已存在
                            var exists = db.Queryable<IFP_UM_USER_INFO>()
                                         .Where(x => (x.UsiLoginName == user.UsiLoginName ||
                                                     x.UsiNumber == user.UsiNumber) && x.Delt == 0)
                                         .Any();

                            if (exists)
                            {
                                // 更新现有用户
                                db.Updateable(user).ExecuteCommand();
                            }
                            else
                            {
                                // 插入新用户
                                db.Insertable(user).ExecuteCommand();
                            }

                            successCount++;
                        }
                        catch (Exception userEx)
                        {
                            failureCount++;
                            errorMessages.Add($"用户 {user.UsiName.Value}: {userEx.Message}");
                            LoggerHelper.Error(ErrorList.E9999, $"批量导入用户失败: {user.UsiName.Value}", userEx);
                        }
                    }

                    db.CommitTran();

                    LoggerHelper.Info($"批量导入完成，成功: {successCount}, 失败: {failureCount}");

                    return new PFActionResult
                    {
                        success = successCount > 0,
                        msg = $"批量导入完成，成功 {successCount} 个，失败 {failureCount} 个",
                        data = new
                        {
                            SuccessCount = successCount,
                            FailureCount = failureCount,
                            ErrorMessages = errorMessages
                        }
                    };
                }
                catch (Exception ex)
                {
                    db.RollbackTran();
                    LoggerHelper.Error(ErrorList.E9999, $"批量导入用户失败: {ex.Message}", ex);
                    return new PFActionResult
                    {
                        success = false,
                        msg = $"批量导入用户失败：{ex.Message}"
                    };
                }
            }
        }

        /// <summary>
        /// 获取用户列表（带扩展信息）
        /// </summary>
        /// <param name="deptGid">部门ID（可选）</param>
        /// <param name="status">状态（可选，0-禁用，1-启用）</param>
        /// <returns>用户列表</returns>
        public PFActionResult GetUserExtList(string deptGid = "", int? status = null)
        {
            using (var db = DB.Create())
            {
                try
                {
                    var query = db.Queryable<IFP_UM_USER_INFO>()
                                 .Where(x => x.Delt == 0);

                    // 部门过滤（如果实现了部门关联）
                    if (!string.IsNullOrWhiteSpace(deptGid))
                    {
                        // 可以根据实际部门关联表进行联表查询
                        // 这里先保留接口，具体实现可根据部门关联表结构调整
                        query = query.Where(x => x.DpmGuid == deptGid);
                    }

                    // 状态过滤（可以根据实际需要扩展状态字段）
                    if (status.HasValue)
                    {
                        // 暂时用删除标记代替状态，可根据实际需要调整
                        if (status.Value == 0)
                        {
                            query = query.Where(x => x.Delt == 1);
                        }
                    }

                    var users = query.OrderBy(x => x.UsiName)
                                    .Select(x => new
                                    {
                                        x.Gid,
                                        x.UsiName,
                                        x.UsiLoginName,
                                        x.UsiNumber,
                                        x.DeptName,
                                        x.DeptFullName,
                                        x.ExtInfo,
                                        x.CreateTime
                                    })
                                    .ToList();

                    // 处理扩展信息
                    var result = users.Select(u => new
                    {
                        u.Gid,
                        u.UsiName,
                        u.UsiLoginName,
                        u.UsiNumber,
                        u.DeptName,
                        u.DeptFullName,
                        u.CreateTime,
                        ExtInfo = ParseExtInfo(u.ExtInfo.Value)
                    }).ToList();

                    return new PFActionResult
                    {
                        success = true,
                        msg = "获取用户列表成功",
                        data = result
                    };
                }
                catch (Exception ex)
                {
                    LoggerHelper.Error(ErrorList.E9999, $"获取用户列表失败: {ex.Message}", ex);
                    return new PFActionResult
                    {
                        success = false,
                        msg = $"获取用户列表失败：{ex.Message}"
                    };
                }
            }
        }

        #region 私有辅助方法

        /// <summary>
        /// 序列化扩展信息到JSON字符串
        /// </summary>
        /// <param name="userInfo">用户信息</param>
        private void SerializeExtInfo(IFP_UM_USER_INFO userInfo)
        {
            try
            {
                if (userInfo.ExtInfoObj != null)
                {
                    var json = JsonConvert.SerializeObject(userInfo.ExtInfoObj);
                    userInfo.ExtInfo = new Field<string>(json);
                    LoggerHelper.Debug($"序列化用户扩展信息: {userInfo.Gid.Value}");
                }
            }
            catch (Exception ex)
            {
                LoggerHelper.Error(ErrorList.E9999, $"序列化用户扩展信息失败: {ex.Message}", ex);
                userInfo.ExtInfo = new Field<string>("");
            }
        }

        /// <summary>
        /// 反序列化JSON字符串到扩展信息对象
        /// </summary>
        /// <param name="userInfo">用户信息</param>
        private void DeserializeExtInfo(IFP_UM_USER_INFO userInfo)
        {
            try
            {
                if (!string.IsNullOrWhiteSpace(userInfo.ExtInfo.Value))
                {
                    userInfo.ExtInfoObj = JsonConvert.DeserializeObject<UserExtInfo>(userInfo.ExtInfo.Value);
                    LoggerHelper.Debug($"反序列化用户扩展信息: {userInfo.Gid.Value}");
                }
                else
                {
                    userInfo.ExtInfoObj = new UserExtInfo();
                }
            }
            catch (Exception ex)
            {
                LoggerHelper.Error(ErrorList.E9999, $"反序列化用户扩展信息失败: {ex.Message}", ex);
                userInfo.ExtInfoObj = new UserExtInfo();
            }
        }

        /// <summary>
        /// 解析扩展信息JSON字符串
        /// </summary>
        /// <param name="extInfoJson">扩展信息JSON</param>
        /// <returns>扩展信息对象</returns>
        private UserExtInfo ParseExtInfo(string extInfoJson)
        {
            try
            {
                if (!string.IsNullOrWhiteSpace(extInfoJson))
                {
                    return JsonConvert.DeserializeObject<UserExtInfo>(extInfoJson);
                }
            }
            catch (Exception ex)
            {
                LoggerHelper.Error(ErrorList.E9999, $"解析扩展信息失败: {ex.Message}", ex);
            }
            return new UserExtInfo();
        }

        /// <summary>
        /// 验证用户信息
        /// </summary>
        /// <param name="userInfo">用户信息</param>
        /// <param name="isNew">是否新增</param>
        /// <returns>验证结果</returns>
        private PFActionResult ValidateUserInfo(IFP_UM_USER_INFO userInfo, bool isNew)
        {
            // 验证必要字段
            if (string.IsNullOrWhiteSpace(userInfo.UsiName.Value))
            {
                return new PFActionResult
                {
                    success = false,
                    msg = "用户名不能为空"
                };
            }

            if (string.IsNullOrWhiteSpace(userInfo.UsiLoginName.Value))
            {
                return new PFActionResult
                {
                    success = false,
                    msg = "登录名不能为空"
                };
            }

            // 验证登录名格式（可根据需要调整规则）
            if (userInfo.UsiLoginName.Value.Length < 3)
            {
                return new PFActionResult
                {
                    success = false,
                    msg = "登录名长度不能少于3位"
                };
            }

            // 验证工号格式（如果有工号）
            if (!string.IsNullOrWhiteSpace(userInfo.UsiNumber.Value) &&
                userInfo.UsiNumber.Value.Length < 2)
            {
                return new PFActionResult
                {
                    success = false,
                    msg = "工号长度不能少于2位"
                };
            }

            return new PFActionResult { success = true };
        }

        /// <summary>
        /// 检查用户是否已存在
        /// </summary>
        /// <param name="db">数据库连接</param>
        /// <param name="userInfo">用户信息</param>
        /// <returns>检查结果</returns>
        private PFActionResult CheckUserExists(SqlSugarClient db, IFP_UM_USER_INFO userInfo)
        {
            // 检查登录名是否已存在
            var existingByLoginName = db.Queryable<IFP_UM_USER_INFO>()
                                       .Where(x => x.UsiLoginName == userInfo.UsiLoginName &&
                                                  x.Gid != userInfo.Gid && x.Delt == 0)
                                       .Any();

            if (existingByLoginName)
            {
                return new PFActionResult
                {
                    success = false,
                    msg = $"登录名 '{userInfo.UsiLoginName.Value}' 已存在"
                };
            }

            // 检查工号是否已存在（如果有工号）
            if (!string.IsNullOrWhiteSpace(userInfo.UsiNumber.Value))
            {
                var existingByNumber = db.Queryable<IFP_UM_USER_INFO>()
                                        .Where(x => x.UsiNumber == userInfo.UsiNumber &&
                                                   x.Gid != userInfo.Gid && x.Delt == 0)
                                        .Any();

                if (existingByNumber)
                {
                    return new PFActionResult
                    {
                        success = false,
                        msg = $"工号 '{userInfo.UsiNumber.Value}' 已存在"
                    };
                }
            }

            return new PFActionResult { success = true };
        }

        #endregion
    }
}