# SPT系统日志优化评估文档

## 概述
当前DAL.SPT.Service类库中共发现**86个**模式2的日志调用，这些日志会直接写入数据库，可能造成日志表快速增长。本文档对所有日志调用进行分类评估，提供优化建议。

## 日志调用统计
- **总计**: 85个模式2日志调用
- **ServiceMain.cs**: 39个
- **CardReader.cs**: 19个
- **TaskExecutor.cs**: 23个
- **TokenService.cs**: 4个

---

## 1. ServiceMain.cs 日志调用分析 (39个)

### 1.1 系统生命周期事件 (9个) - 🔴 建议保留

| 行号 | 代码 | 建议 | 执行 |
|------|------|------|------|
| 64 | `LogUtility.ToNotice("1#PLC点位开始轮询", 2);` | 保留 |  |
| 84 | `LogUtility.ToNotice("后台服务关闭", 2);` | 保留 |  |
| 88 | `LogUtility.ToNotice("1#PLC点位轮询被关闭", 2);` | 保留 |  |
| 145 | `LogUtility.ToNotice("后台服务启动成功", 2);` | 保留 |  |
| 218 | `LogUtility.ToNotice("自动重启服务成功", 2);` | 保留 |  |
| 331 | `LogUtility.ToNotice($"初始化步骤3，数据缓存器构建成功，点表长度{Global.DataMemory.Data.Count}", 2);` | 保留 |  |
| 356 | `LogUtility.ToNotice("初始化步骤4，服务类初始化成功", 2);` | 保留 |  |
| 365 | `LogUtility.ToNotice("初始化步骤5，业务服务初始化成功", 2);` | 保留 |  |
| 581 | `LogUtility.ToNotice("启动获取数据服务", 2);` | 保留 |  |

### 1.2 服务启动/停止事件 (12个) - 🔴 建议保留

| 行号 | 代码 | 建议 | 执行 |
|------|------|------|------|
| 190 | `LogUtility.ToNotice("启动自动重启服务功能", 2);` | 保留 |  |
| 260 | `LogUtility.ToNotice("启动自动重连服务守护线程", 2);` | 保留 |  |
| 403 | `LogUtility.ToNotice("启动自动报警监控", 2);` | 保留 |  |
| 410 | `LogUtility.ToNotice("自动报警监控被关闭", 2);` | 保留 |  |
| 437 | `LogUtility.ToNotice("自动读卡服务启动", 2);` | 保留 |  |
| 458 | `LogUtility.ToNotice("自动读卡服务停止", 2);` | 保留 |  |
| 477 | `LogUtility.ToNotice("自动任务管理服务启动", 2);` | 保留 |  |
| 489 | `LogUtility.ToNotice("自动任务管理服务停止", 2);` | 保留 |  |
| 508 | `LogUtility.ToNotice("令牌管理服务启动", 2);` | 保留 |  |
| 520 | `LogUtility.ToNotice("令牌管理服务停止", 2);` | 保留 |  |
| 539 | `LogUtility.ToNotice("传瓶进度服务启动", 2);` | 保留 |  |
| 565 | `LogUtility.ToNotice("传瓶进度服务停止", 2);` | 保留 |  |

### 1.3 系统状态监控 (6个) - 🟡 建议降级

| 行号 | 代码 | 建议 | 执行 |
|------|------|------|------|
| 78 | `LogUtility.ToNotice($"AssGlobal.RUN_STATUS={Global.RUN_STATUS};AssGlobal.PlcIsConnected={Global.PlcIsConnected};", 2);` | 降级为模式0 |  |
| 110 | `LogUtility.ToNotice($"轮询间隔超时({Global.PointReadDelay}ms)，PLC断开连接", 2);` | 降级为模式0 |  |
| 598 | `LogUtility.ToNotice("获取数据服务停止", 2);` | 降级为模式0 |  |

### 1.4 错误和异常 (12个) - 🔴 建议保留

| 行号 | 代码 | 建议 | 执行 |
|------|------|------|------|
| 99 | `LogUtility.ToError($"1#PLC点位轮询，更新点位异常。", 2, e);` | 保留 |  |
| 123 | `LogUtility.ToError($"1#PLC点位轮询，时间统计异常。\r\n{e}", 2);` | 保留 |  |
| 222 | `LogUtility.ToError("自动重启服务失败", 2, e);` | 保留 |  |
| 228 | `LogUtility.ToNotice($"自动重启服务失败。{e}", 2);` | 保留 |  |
| 304 | `LogUtility.ToError("PLC连接失败", 2, e);` | 保留 |  |
| 316 | `LogUtility.ToError("数据缓存器构建失败，没有找到点表源数据", 2);` | 保留 |  |
| 335 | `LogUtility.ToError("初始化步骤3，数据缓存器构建失败", 2, e);` | 保留 |  |
| 389 | `LogUtility.ToError("后台服务启动失败", 2, e);` | 保留 |  |
| 418 | `LogUtility.ToError("自动报警监控异常", 2, e);` | 保留 |  |
| 450 | `LogUtility.ToError("读卡服务执行异常", 2, ex);` | 保留 |  |
| 463 | `LogUtility.ToError("自动读卡服务异常停止", 2, ex);` | 保留 |  |
| 494 | `LogUtility.ToError("自动任务管理服务异常停止", 2, ex);` | 保留 |  |
| 525 | `LogUtility.ToError("令牌管理服务异常停止", 2, ex);` | 保留 |  |
| 570 | `LogUtility.ToError("传瓶进度服务异常停止", 2, ex);` | 保留 |  |
| 591 | `LogUtility.ToError("获取数据服务异常", 2, e);` | 保留 |  |

---

## 2. CardReader.cs 日志调用分析 (19个)

### 2.1 读卡操作详情 (8个) - 🟡 建议降级

| 行号 | 代码 | 建议 | 执行 |
|------|------|------|------|
| 207 | `LogUtility.ToNotice($"设备未就绪，跳过站点{stationCode}读卡", 2);` | 降级为模式0 |  |
| 223 | `LogUtility.ToNotice($"站点{stationCode}读到卡号: {cardNo}", 2);` | 降级为模式0 |  |
| 250 | `LogUtility.ToNotice($"站点{stationCode}没有可用的目标站点，无法创建任务，来源:{source}", 2);` | 降级为模式0 |  |
| 262 | `LogUtility.ToNotice($"已存在未执行的任务，卡号:{cardNo}，跳过创建，来源:{source}", 2);` | 降级为模式0 |  |
| 312 | `LogUtility.ToNotice($"找到精确匹配规则，卡号:{cardNo}，起始站点:{originStation}，目标站点:{exactRule.TARGET.Value}，瓶子类型:{exactRule.BOLT.Value}", 2);` | 降级为模式0 |  |
| 324 | `LogUtility.ToNotice($"找到通配符规则，卡号:{cardNo}，起始站点:{originStation}，目标站点:{wildcardRule.TARGET.Value}，瓶子类型:{wildcardRule.BOLT.Value}", 2);` | 降级为模式0 |  |
| 408 | `LogUtility.ToNotice($"自动初始化虚拟读卡器：站点{station.CODE.Value}({station.NAME.Value})", 2);` | 降级为模式0 |  |
| 436 | `LogUtility.ToNotice($"虚拟读卡器读到卡号：站点{kvp.Key}，卡号{data.Card}", 2);` | 降级为模式0 |  |

### 2.2 任务创建成功 (2个) - 🔴 建议保留

| 行号 | 代码 | 建议 | 执行 |
|------|------|------|------|
| 283 | `LogUtility.ToNotice($"创建任务成功，站点:{stationCode}，卡号:{cardNo}，目标站点:{targetStation}，瓶子类型:{boltType}，来源:{source}", 2);` | 保留 |  |
| 413 | `LogUtility.ToNotice($"虚拟读卡器初始化完成，共{_virtualReaders.Count}个", 2);` | 保留 |  |

### 2.3 读卡器控制 (2个) - 🔴 建议保留

| 行号 | 代码 | 建议 | 执行 |
|------|------|------|------|
| 483 | `LogUtility.ToNotice("读卡器已暂停", 2);` | 保留 |  |
| 492 | `LogUtility.ToNotice("读卡器已恢复", 2);` | 保留 |  |

### 2.4 错误和异常 (7个) - 🔴 建议保留

| 行号 | 代码 | 建议 | 执行 |
|------|------|------|------|
| 169 | `LogUtility.ToError($"站点{stationCode}读卡异常", 2, ex);` | 保留 |  |
| 176 | `LogUtility.ToError("读卡服务检查异常", 2, ex);` | 保留 |  |
| 237 | `LogUtility.ToError($"站点{stationCode}读卡异常", 2, ex);` | 保留 |  |
| 288 | `LogUtility.ToError($"创建任务失败，站点:{stationCode}，卡号:{cardNo}，来源:{source}", 2, ex);` | 保留 |  |
| 350 | `LogUtility.ToError($"确定目标站点和瓶子大小失败", 2, ex);` | 保留 |  |
| 441 | `LogUtility.ToError($"虚拟读卡器{kvp.Key}读卡异常", 2, ex);` | 保留 |  |
| 470 | `LogUtility.ToError("处理虚拟读卡器异常", 2, ex);` | 保留 |  |

---

## 3. TokenService.cs 日志调用分析 (4个)

### 3.1 令牌管理核心事件 (4个) - 🔴 建议保留

| 行号 | 代码 | 建议 | 执行 |
|------|------|------|------|
| 89 | `LogUtility.ToNotice($"站点{stationCode}主动取消令牌请求，立即收回令牌", 2);` | 保留 |  |
| 109 | `LogUtility.ToError($"检测到令牌冲突！同时有{_allowedStations.Count}个站点被授予令牌：{string.Join(",", _allowedStations.Select(s => s.CODE.Value))}", 2);` | 保留 |  |
| 138 | `LogUtility.ToNotice($"授予令牌给站点{stationCode}({selectedStation.NAME.Value})", 2);` | 保留 |  |
| 145 | `LogUtility.ToError("令牌服务处理异常", 2, ex);` | 保留 |  |

---

## 4. TaskExecutor.cs 日志调用分析 (23个)

### 4.1 任务执行核心事件 (6个) - 🔴 建议保留

| 行号 | 代码 | 建议 | 执行 |
|------|------|------|------|
| 129 | `LogUtility.ToNotice($"恢复执行中任务：样品={_currentExecuteTask.SAMPLE.Value}", 2);` | 保留 |  |
| 145 | `LogUtility.ToNotice($"清理已完成任务：样品={_currentExecuteTask.SAMPLE.Value}, 状态={_currentExecuteTask.STATUS}", 2);` | 保留 |  |
| 179 | `LogUtility.ToNotice($"启动{taskType}任务：样品={nextTask.SAMPLE.Value}", 2);` | 保留 |  |
| 190 | `LogUtility.ToError($"任务执行条件不满足，标记为失败：样品={nextTask.SAMPLE.Value}", 2);` | 保留 |  |
| 221 | `LogUtility.ToNotice($"目标站暂时不能接收，任务({_currentExecuteTask.SAMPLE.Value})恢复到未执行状态", 2);` | 保留 |  |
| 240 | `LogUtility.ToNotice($"任务状态更新：样品={task.SAMPLE.Value}, 状态={point.Value.Message}", 2);` | 保留 |  |

### 4.2 任务条件检查详情 (7个) - 🟡 建议降级

| 行号 | 代码 | 建议 | 执行 |
|------|------|------|------|
| 302 | `LogUtility.ToNotice($"起点站{originStationId}通讯中断，无法下发任务({task.SAMPLE.Value})", 2);` | 降级为模式0 |  |
| 308 | `LogUtility.ToNotice($"目标站{targetStationId}通讯中断，无法下发任务({task.SAMPLE.Value})", 2);` | 降级为模式0 |  |
| 314 | `LogUtility.ToNotice($"起点站{originStationId}没有检测到样瓶，无法下发任务({task.SAMPLE.Value})", 2);` | 降级为模式0 |  |
| 321 | `LogUtility.ToNotice($"目标站{targetStationId}已存在样瓶，无法下发任务({task.SAMPLE.Value})", 2);` | 降级为模式0 |  |
| 345 | `LogUtility.ToNotice($"目标站{targetStationId}未准备就绪，无法下发任务({task.SAMPLE.Value})", 2);` | 降级为模式0 |  |
| 366 | `LogUtility.ToNotice(message, 2);` | 降级为模式0 |  |

### 4.3 点位写入错误 (10个) - 🔴 建议保留

| 行号 | 代码 | 建议 | 执行 |
|------|------|------|------|
| 113 | `LogUtility.ToError("点位存储器未初始化", 2);` | 保留 |  |
| 183 | `LogUtility.ToError($"下发任务失败：样品={nextTask.SAMPLE.Value}", 2, ex);` | 保留 |  |
| 354 | `LogUtility.ToError($"检查任务条件异常：{task.SAMPLE.Value}", 2, ex);` | 保留 |  |
| 383 | `LogUtility.ToError(errorMsg, 2);` | 保留 |  |
| 391 | `LogUtility.ToError(errorMsg, 2);` | 保留 |  |
| 399 | `LogUtility.ToError(errorMsg, 2);` | 保留 |  |
| 407 | `LogUtility.ToError(errorMsg, 2);` | 保留 |  |
| 414 | `LogUtility.ToError(errorMsg, 2);` | 保留 |  |
| 424 | `LogUtility.ToError(errorMsg, 2);` | 保留 |  |
| 434 | `LogUtility.ToError(errorMsg, 2);` | 保留 |  |

---

## 5. 优化建议汇总

### 🔴 保留数据库记录 (52个)
- **系统生命周期**: 服务启动/停止、初始化完成 (21个)
- **任务核心事件**: 任务创建、执行、状态变更 (8个)
- **令牌管理**: 令牌授予、冲突检测、服务异常 (4个)
- **严重错误**: 所有异常和错误日志 (19个)

### 🟡 降级为模式0 (33个)
- **读卡详情**: 读卡成功、规则匹配、设备状态检查 (8个)
- **任务检查**: 条件检查、状态验证、前置条件 (13个)
- **系统监控**: PLC轮询状态、连接状态变化 (3个)
- **调试信息**: 详细的操作步骤和状态信息 (9个)

### 📊 预期效果
- **数据库日志减少**: 约39%的日志调用降级 (33/85)
- **保留核心追踪**: 所有关键业务节点仍可追溯
- **提升性能**: 减少数据库写入压力
- **便于维护**: 核心日志更加突出，便于问题定位

---

## 6. 执行说明

请在每个日志条目的"执行"列中填写：
- **Y**: 同意建议，执行修改
- **N**: 不同意建议，保持现状
- **M**: 需要修改建议（请注明具体要求）

完成评估后，我将根据您的决定批量执行相应的修改。
