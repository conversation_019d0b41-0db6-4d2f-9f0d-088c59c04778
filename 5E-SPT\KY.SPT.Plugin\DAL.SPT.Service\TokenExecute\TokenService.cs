using COM.IFP.Log;
using COM.IFP.SqlSugarN;
using ORM.SPT;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DAL.SPT.Service.TokenExecute
{
    /// <summary>
    /// 令牌服务 - 管理SPT系统中的站点令牌分配
    /// </summary>
    public class TokenService
    {
        #region 私有字段

        /// <summary>
        /// 全局锁，确保令牌操作的线程安全
        /// </summary>
        private static readonly object _tokenLock = new object();

        /// <summary>
        /// 当前授予令牌的站点编码
        /// </summary>
        private static string? _conferStation = null;

        /// <summary>
        /// 请求令牌的站点队列
        /// </summary>
        private static readonly List<SPT_STATION> _demandStations = new List<SPT_STATION>();

        /// <summary>
        /// 已获得令牌的站点列表  
        /// </summary>
        private static readonly List<SPT_STATION> _allowedStations = new List<SPT_STATION>();

        /// <summary>
        /// 令牌授予时间
        /// </summary>
        private static DateTime _grantTime = DateTime.MinValue;
       
        /// <summary>
        /// 构造函数
        /// </summary>
        public TokenService()
        {

        }
        #endregion

        #region 核心令牌处理方法

        /// <summary>
        /// 检查并处理令牌请求的主方法
        /// </summary>
        public void CheckAndProcessTokenRequests()
        {
            if (Global.DataMemory == null) return;

            lock (_tokenLock)
            {
                try
                {
                    // 清空临时列表
                    _demandStations.Clear();
                    _allowedStations.Clear();

                    // 获取需要令牌授权的站点
                    using var db = DB.Create();
                    var stations = db.Queryable<SPT_STATION>()
                                     .Where(s => s.VALID == 1 && IsTokenRequiredStation(s.CODE.Value))
                                     .ToList();

                    foreach (var station in stations)
                    {
                        string stationCode = station.CODE.Value;

                        // 检查站点是否请求令牌
                        bool isRequesting = Global.CheckPointValue(stationCode + Constant.QQLPZT, true);

                        if (isRequesting)
                        {
                            _demandStations.Add(station);
                        }
                        else
                        {
                            if (stationCode == _conferStation)
                            {
                                LogUtility.ToNotice($"站点{stationCode}主动取消令牌请求，立即收回令牌", 2);

                                Global.WritePointValue(stationCode + Constant.ZDLPML, false);
                                Global.WritePointValue(stationCode + Constant.SYLPZT, false);

                                _conferStation = null;
                                _grantTime = DateTime.MinValue;
                            }
                        }

                        // 检查已获得令牌状态（用于冲突检测）
                        if (Global.CheckPointValue(stationCode + Constant.ZDLPML, true))
                        {
                            _allowedStations.Add(station);
                        }
                    }

                    // 冲突WPF是采用停机手动干预的方式，改成了自动处理 但是可能需要结合实际重新考量
                    if (_allowedStations.Count > 1)
                    {
                        LogUtility.ToError($"检测到令牌冲突！同时有{_allowedStations.Count}个站点被授予令牌：{string.Join(",", _allowedStations.Select(s => s.CODE.Value))}", 2);
                        
                        foreach (var conflictStation in _allowedStations)
                        {
                            Global.WritePointValue(conflictStation.CODE.Value + Constant.ZDLPML, false);
                            Global.WritePointValue(conflictStation.CODE.Value + Constant.SYLPZT, false);
                        }
                        
                        _conferStation = null;
                        _grantTime = DateTime.MinValue;
                        _allowedStations.Clear();
                    }

                    // 令牌授予逻辑
                    if (string.IsNullOrEmpty(_conferStation))
                    {
                        // 没有令牌授予，随机选择一个请求站点授予令牌
                        if (_demandStations.Count > 0)
                        {
                            var selectedStation = _demandStations.Count == 1
                                ? _demandStations[0]
                                : _demandStations[new Random().Next(0, _demandStations.Count)];

                            string stationCode = selectedStation.CODE.Value;

                            if (Global.WritePointValue(stationCode + Constant.ZDLPML, true))
                            {
                                _conferStation = stationCode;
                                _grantTime = DateTime.Now;
                                LogUtility.ToNotice($"授予令牌给站点{stationCode}({selectedStation.NAME.Value})", 2);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    LogUtility.ToError("令牌服务处理异常", 2, ex);
                }
            }
        }
        #endregion

        #region 辅助方法

        /// <summary>
        /// 判断站点是否需要令牌授权
        /// </summary>
        /// <param name="stationCode">站点编码</param>
        /// <returns>是否需要令牌</returns>
        private static bool IsTokenRequiredStation(string stationCode)
        {
            if (string.IsNullOrEmpty(stationCode))
                return false;

            // 样柜站：6x
            if (stationCode.StartsWith("6"))
                return true;

            // 人工站：7x  
            if (stationCode.StartsWith("7"))
                return true;
          
            // 机器人制样收发站：110-119 (三位数，110开头)
            if (stationCode.Length >= 3 && stationCode.StartsWith("11"))
                return true;

            return false;
        }
        #endregion
    }
} 