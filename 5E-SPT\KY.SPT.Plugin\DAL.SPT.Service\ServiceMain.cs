﻿using DAL.SPT.Service.Diagram;
using DAL.SPT.Service.PipelineProgress;
using DAL.SPT.Service.CardReaders;
using ORM.SPT;
using DAL.SPT.Service.TokenExecute;

namespace DAL.SPT.Service
{
    public class ServiceMain
    {
        public static ServiceMain assServiceMain = new ServiceMain();

        /// <summary>
        /// 用于比对自动重连线程是否abort，是的话重启线程。
        /// </summary>
        uint lastAccumulator = 0;
        /// <summary>
        /// 用于比对自动重连线程是否abort，是的话重启线程。
        /// </summary>
        uint accumulator = 1;

        private ServiceMain()
        {
        }
        #region 
        void PointRefresh(Point point)
        {
            LogUtility.ToNotice($"点位值变化{point.Name}[{point.ModbusP.StartAdd}.{point.ModbusP.BitOffset}][{point.PointDefine.MODBUS.Value}]={point.Value}");

        }

        void PointWrite(Point point, object val)
        {
            LogUtility.ToNotice($"写点位值{point.Name}={val}");
        }
        #endregion
        /// <summary>
        /// 轮询PLC点位
        /// </summary>
        private void EchoPoint()
        {
            Task.Factory.StartNew(() =>
            {
#if LookThreadNum
                using (System.IO.StreamWriter sr = new System.IO.StreamWriter("D:/ThreadNum.txt", true))
                {
                    //打印线程号
                    string sTmp = "0x" + Thread.CurrentThread.ManagedThreadId.ToString("X");
                    string line = $"{DateTime.Now.ToString()}#{System.Reflection.MethodBase.GetCurrentMethod().Name}#{sTmp};";
                    sr.WriteLine(line);
                }
#endif
                Global.NO_OVER_TIME = true;
                //NO_SOCKET_EXCEPTION = true;
                //等待socket连通
                if (Global.PlcIsConnected == false)
                {
                    Thread.Sleep(2000);
                }
                //if (AssGlobal.REDIS_STATUS == false)
                //{
                //    Thread.Sleep(2000);
                //}
                LogUtility.ToNotice("1#PLC点位开始轮询", 2);
                //标记开始轮询
                Global.ECHO_POINT_STATUS = true;
                //重启服务后防止时间过久超时
                Global.DataMemory.LastMoment = DateTime.Now;
                //记录一次轮询结束的时刻
                DateTime lastTime;
                //重新统计采集系统心跳

                while (true)
                {
                    if (!Global.RUN_STATUS ||
                    !Global.PlcIsConnected)
                    {
                        LogUtility.ToNotice($"AssGlobal.RUN_STATUS={Global.RUN_STATUS};AssGlobal.PlcIsConnected={Global.PlcIsConnected};", 2);

                        Global.PointReadDelay = -1;

                        //控制其他子线程return;
                        Global.RUN_STATUS = false;
                        LogUtility.ToNotice("后台服务关闭", 2);

                        //标记关闭轮询
                        Global.ECHO_POINT_STATUS = false;
                        LogUtility.ToNotice("1#PLC点位轮询被关闭", 2);
                        return;
                    }
                    lastTime = Global.DataMemory.LastMoment;
                    //防止抛出异常后退出循环线程
                    try
                    {
                        Global.DataMemory.RefreshAllPoints();
                    }
                    catch (Exception e)
                    {
                        LogUtility.ToError($"1#PLC点位轮询，更新点位异常。", 2, e);
                    }
                    try
                    {
                        double delay = (DateTime.Now - lastTime).TotalMilliseconds;
                        //轮询间隔超过最大间隔辅助判定PLC断开连通
                        if (Global.RUN_STATUS == true
                        && delay > Global.MAX_INTERVALS)
                        {
                            Global.NO_OVER_TIME = false;
                            Global.PointReadDelay = delay;
                            LogUtility.ToNotice($"轮询间隔超时({Global.PointReadDelay}ms)，PLC断开连接", 2);
                        }

                        //限制最少时间间隔
                        if (delay < Global.MIN_INTERVALS)
                        {
                            int t = Global.MIN_INTERVALS - (int)delay;
                            Thread.Sleep(t);
                            Global.PointReadDelay = (DateTime.Now - lastTime).TotalMilliseconds;
                        }
                    }
                    catch (Exception e)
                    {
                        LogUtility.ToError($"1#PLC点位轮询，时间统计异常。\r\n{e}", 2);
                    }

#if debug
                    Task.Run(() =>
                    {
                        LoggerHelper.Info($"【RefreshAll】{AssGlobal.PointReadDelay}ms");
                    });
#endif
                }
            }, TaskCreationOptions.LongRunning);
        }

        #region

        public void ServiceStart()
        {
            try
            {
                //0、写入开启标记，后面都是异步循环方法，如果不先置位，
                //那么后面的方法可能会判断RunStatus==false从而停止。
                Global.RUN_STATUS = true;
                LogUtility.ToNotice("后台服务启动成功", 2);

                //1、开始轮询点位
                EchoPoint();
                //2、更新下位机参数
                //Params2PLC();
                //3、开启报警监控
                EchoAlramTask();
                //4、启动令牌服务
                EchoTokenService();
                //5、开启读卡器
                EchoCardReader();
                //6、开启自动任务管理
                EchoTaskExecutor();
                //7.开启获取数据服务
                EchoGetData();
                //8、初始化传瓶进度服务
                EchoPipelineProgress();
                //Global.UC.EchoAutoGasTakeBack();
                //启动三个服务
               
            }
            catch (Exception e)
            {
                Global.RUN_STATUS = false;
                throw;
            }
        }

        /// <summary>
        /// 自动重连线程守护
        /// </summary>
        public void AutoRestart()
        {
            Task.Factory.StartNew(() =>
            {
#if LookThreadNum
                using (System.IO.StreamWriter sr = new System.IO.StreamWriter("D:/ThreadNum.txt", true))
                {
                    //打印线程号
                    string sTmp = "0x" + Thread.CurrentThread.ManagedThreadId.ToString("X");
                    string line = $"{DateTime.Now.ToString()}#{System.Reflection.MethodBase.GetCurrentMethod().Name}#{sTmp};";
                    sr.WriteLine(line);
                }
#endif
                LogUtility.ToNotice("启动自动重启服务功能", 2);
                while (true)
                {
                    try
                    {
                        //如果服务关闭，其他子线程也要结束，不然因为异步，启动又被没结束的子线程关闭服务
                        //已经改成只有轮询线程内能设置标志关闭其他子线程。
                        if (Global.RUN_STATUS == false &&
                        Global.ECHO_POINT_STATUS == false)
                        {
                            try
                            {
                                Global.ModbusAdaptor.Build();

                                if (Global.ModbusAdaptor.GetStatus() == false)
                                {
                                    continue;
                                }
                                try
                                {
                                    // 减去触发任务的委托，防止重复订阅
                                    if (Global.DataMemory != null)
                                    {
                                        //由下位机点位上升沿跳变触发的功能块，将触发任务委托到点位值更新事件
                                        Global.DataMemory.OnPointRefresh -= PointRefresh;
                                    }

                                    ServiceStart();
                                    LogUtility.ToNotice("自动重启服务成功", 2);
                                }
                                catch (Exception e)
                                {
                                    LogUtility.ToError("自动重启服务失败", 2, e);
                                }

                            }
                            catch (Exception e)
                            {
                                LogUtility.ToNotice($"自动重启服务失败。{e}", 2);
                            }
                        }
                    }
                    catch { }
                    finally
                    {
                        ++accumulator;
                        Thread.Sleep(Global.CHECK_INTERVALS);
                    }
                }
            }, TaskCreationOptions.LongRunning);
        }
        /// <summary>
        /// 自动重连线程守护
        /// </summary>
        public void AutoRestartGuard()
        {
            Task.Factory.StartNew(() =>
            {
#if LookThreadNum
                Task.Run(() =>
                {
                    using (System.IO.StreamWriter sr = new System.IO.StreamWriter("D:/ThreadNum.txt", true))
                    {
                        //打印线程号
                        string sTmp = "0x" + Thread.CurrentThread.ManagedThreadId.ToString("X");
                        string line = $"{DateTime.Now.ToString()}#{System.Reflection.MethodBase.GetCurrentMethod().Name}#{sTmp};";
                        sr.WriteLine(line);
                    }
                });
#endif
                LogUtility.ToNotice("启动自动重连服务守护线程", 2);
                while (true)
                {
                    if (lastAccumulator != accumulator)
                    {
                        lastAccumulator = accumulator;
                    }
                    else
                    {
                        AutoRestart();
                    }
                    Thread.Sleep(Global.CHECK_INTERVALS * 4);
                }
            }, TaskCreationOptions.LongRunning);

        }
        #endregion  


        //static List<Point> AllPoint;
        /// <summary>
        /// 初始化操作
        /// </summary>
        public void Init()
        {

            #region 处理PLC通讯初始化
            try
            {

                var config = COM.IFP.Common.Config.LocalSystem.GetProperty("PLC");


                string ip = config.GetProperty("IP").GetString();
                int port = config.GetProperty("Port").GetInt32();
                int slot = config.GetProperty("Slot").GetInt32();
                var ada = new COM.IFP.PLC.Modbus.ModbusTCPAdaptor(ip, port);
                Global.ModbusAdaptor = ada;

                ada.Unit = Convert.ToByte(slot);
                ada.Build();
            }
            catch (Exception e)
            {
                LogUtility.ToError("PLC连接失败", 2, e);
            }
            #endregion

            #region 初始化数据缓存器
            try
            {
                //todo是否只读取启用站点的点？
                //连续读取，跳着读
                List<SPT_POINT> source = Global.DB_Option.Value.SelectValidPoint();
                if (source.Count < 1)
                {
                    LogUtility.ToError("数据缓存器构建失败，没有找到点表源数据", 2);
                    return;
                }

                Global.DataMemory = new DataStorage();
                List<Point> newSource = Global.DataMemory.PointConvert(source);
                Global.AllPoint = newSource;

                Global.DataMemory.Import(newSource);

                Global.DataMemory.Inject(Global.ModbusAdaptor);

                Global.DataMemory.OnPointRefresh += PointRefresh;
                Global.DataMemory.OnPointWrite += PointWrite;

                LogUtility.ToNotice($"初始化步骤3，数据缓存器构建成功，点表长度{Global.DataMemory.Data.Count}", 2);
            }
            catch (Exception e)
            {
                LogUtility.ToError("初始化步骤3，数据缓存器构建失败", 2, e);
                return;
            }

            #endregion

            #region LowerMachine

            #endregion

            #region 监视器

            #endregion

            #region CPU
            Global.Execute = new Execute();
            Global.Execute.Inject(Global.DataMemory);

            Global.CPU = new ServiceCPU();
            Global.CPU.Inject(Global.Execute, Global.DataMemory);

            LogUtility.ToNotice("初始化步骤4，服务类初始化成功", 2);
            #endregion

            #region 业务服务初始化
            Global.CardReaderService = new CardReader();
            Global.TaskExecutorService = new TaskExecutor();
            Global.PipelineProgressService = new PipelineProgressService();
            Global.TokenService = new TokenService();

            LogUtility.ToNotice("初始化步骤5，业务服务初始化成功", 2);

            //防止空引用
            Global.CurrentPipelineProgressData = new List<TaskStationData>();
            Global.UnfinishedTaskListDatas = new List<Dictionary<string, object>>();
            Global.AlarmListData = new List<Dictionary<string, object>>();
            Global.RunInfoListData = new List<SPT_PROCESS_INFO>();
            #endregion

            #region 初始化udp发送

            #endregion


            #region 启动服务及服务自动重
            try
            {
                ServiceStart();
                Thread.Sleep(500);
                AutoRestart();
                AutoRestartGuard();
            }
            catch (Exception e)
            {
                LogUtility.ToError("后台服务启动失败", 2, e);
            }
            #endregion
        }


        #region 自动功能线程
        /// <summary>
        /// 自动报警监控
        /// </summary>
        private void EchoAlramTask()
        {
            Task.Factory.StartNew(() =>
            {
                LogUtility.ToNotice("启动自动报警监控", 2);
                while (true)
                {
                    try
                    {
                        if (!Global.RUN_STATUS || !Global.PlcIsConnected)
                        {
                            LogUtility.ToNotice("自动报警监控被关闭", 2);
                            return;
                        }

                        Global.CPU.AutoAlarm();
                    }
                    catch (Exception e)
                    {
                        LogUtility.ToError("自动报警监控异常", 2, e);
                    }
                    finally
                    {
                        Thread.Sleep(2000); // 每2秒检查一次报警
                    }
                }
            }, TaskCreationOptions.LongRunning);
        }

        /// <summary>
        /// 启动读卡器服务
        /// </summary>
        private void EchoCardReader()
        {
            Task.Factory.StartNew(() =>
            {
                try
                {
                    LogUtility.ToNotice("自动读卡服务启动", 2);
                    // 标记读卡器服务启动
                    Global.CARD_READER_RUNNING = true;

                    // 持续监控服务状态
                    while (Global.RUN_STATUS)
                    {
                        try
                        {
                            Global.CardReaderService.CheckAndReadAllCards();
                        }
                        catch (Exception ex)
                        {
                            LogUtility.ToError("读卡服务执行异常", 2, ex);
                        }
                        // 按物理读卡器频率检查
                        Thread.Sleep(1000);
                    }

                    // 标记读卡器服务停止
                    Global.CARD_READER_RUNNING = false;
                    LogUtility.ToNotice("自动读卡服务停止", 2);
                }
                catch (Exception ex)
                {
                    Global.CARD_READER_RUNNING = false;
                    LogUtility.ToError("自动读卡服务异常停止", 2, ex);
                }
            }, TaskCreationOptions.LongRunning);
        }

        /// <summary>
        /// 启动任务执行器服务
        /// </summary>
        private void EchoTaskExecutor()
        {
            Task.Factory.StartNew(() =>
            {
                try
                {
                    LogUtility.ToNotice("自动任务管理服务启动", 2);
                    // 标记自动任务服务启动
                    Global.TASK_EXECUTOR_RUNNING = true;

                    while (Global.RUN_STATUS)
                    {
                        Global.TaskExecutorService.CheckAndExecuteNextTask();
                        // 每隔1秒检查一次
                        Thread.Sleep(1000);
                    }
                    // 标记自动任务服务停止
                    Global.TASK_EXECUTOR_RUNNING = false;
                    LogUtility.ToNotice("自动任务管理服务停止", 2);
                }
                catch (Exception ex)
                {
                    Global.TASK_EXECUTOR_RUNNING = false;
                    LogUtility.ToError("自动任务管理服务异常停止", 2, ex);
                }
            }, TaskCreationOptions.LongRunning);
        }

        /// <summary>
        /// 启动令牌服务
        /// </summary>
        private void EchoTokenService()
        {
            Task.Factory.StartNew(() =>
            {
                try
                {
                    LogUtility.ToNotice("令牌管理服务启动", 2);
                    // 标记令牌服务启动
                    Global.TOKEN_SERVICE_RUNNING = true;

                    while (Global.RUN_STATUS)
                    {
                        Global.TokenService.CheckAndProcessTokenRequests();
                        // 每隔1秒检查一次令牌请求
                        Thread.Sleep(1000);
                    }
                    // 标记令牌服务停止
                    Global.TOKEN_SERVICE_RUNNING = false;
                    LogUtility.ToNotice("令牌管理服务停止", 2);
                }
                catch (Exception ex)
                {
                    Global.TOKEN_SERVICE_RUNNING = false;
                    LogUtility.ToError("令牌管理服务异常停止", 2, ex);
                }
            }, TaskCreationOptions.LongRunning);
        }

        /// <summary>
        /// 启动传瓶进度服务
        /// </summary>
        private void EchoPipelineProgress()
        {
            Task.Factory.StartNew(() =>
            {
                try
                {
                    LogUtility.ToNotice("传瓶进度服务启动", 2);
                    Global.PIPELINE_PROGRESS_RUNNING = true;

                    // 持续监控服务状态
                    while (Global.RUN_STATUS)
                    {
                        try
                        {
                            // 获取传瓶进度数据并推送
                            var progressData = Global.PipelineProgressService.GetPipelineProgressData();
                            if (progressData?.taskData?.Count > 0)
                            {
                                // 将数据存储到全局变量，供Interface类使用
                                Global.CurrentPipelineProgressData = progressData.taskData;
                            }
                        }
                        catch (Exception ex)
                        {
                            LogUtility.ToError("获取或推送传瓶进度数据失败", ex);
                        }

                        // 每隔1秒检查一次
                        Thread.Sleep(1000);
                    }

                    Global.PIPELINE_PROGRESS_RUNNING = false;
                    LogUtility.ToNotice("传瓶进度服务停止", 2);
                }
                catch (Exception ex)
                {
                    Global.PIPELINE_PROGRESS_RUNNING = false;
                    LogUtility.ToError("传瓶进度服务异常停止", 2, ex);
                }
            }, TaskCreationOptions.LongRunning);
        }
        /// <summary>
        /// 获取数据服务
        /// </summary>
        private void EchoGetData()
        {
            Task.Factory.StartNew(() =>
            {
                LogUtility.ToNotice("启动获取数据服务", 2);
                while (Global.RUN_STATUS)
                {
                    try
                    {
                        // 获取数据逻辑
                        Global.CPU.AutoGetData();
                    }
                    catch (Exception e)
                    {
                        LogUtility.ToError("获取数据服务异常", 2, e);
                    }
                    finally
                    {
                        Thread.Sleep(1000); // 每秒获取一次数据
                    }
                }
                LogUtility.ToNotice("获取数据服务停止", 2);
            }, TaskCreationOptions.LongRunning);
        }


        #endregion

        #region

        #endregion
        /// <summary>
        /// 由框架调用启动本服务，配置Launch.json
        /// </summary>
        public static void Start()
        {
            Task.Run(() =>
            {
                assServiceMain.Init();
                ServiceDiagram.Init();
            }
            );
        }
    }
}
