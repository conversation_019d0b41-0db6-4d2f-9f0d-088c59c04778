﻿using COM.IFP.Common;
using COM.IFP.Log;
using DAL.IFP.Rights;
using Newtonsoft.Json;
using ORM.IFP;
using ORM.IFP.DTO;
using ORM.IFP.www.DbModel.UM;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;

namespace API.IFP.Rights
{
    /// <summary>
    /// 用户信息控制器
    /// </summary>
    public class Role
    {
        private Lazy<DAL.IFP.Rights.Role> _service = Entity.Create<DAL.IFP.Rights.Role>();

        public List<IFP_UM_MENU> RoleMenu(JsonElement obj)
        {
            string roleGid = obj.GetValue<string>("roleGid");

            return _service.Value.RoleMenu(roleGid);
        }

        public List<IFP_UM_ROLE> RoleList(JsonElement obj)
        {
            return _service.Value.RoleList();
        }
        public List<IFP_UM_ROLE> MyRoleList(JsonElement obj)
        {
            return _service.Value.MyRoleList();
        }

        /// <summary>
        /// 角色管理查询
        /// </summary>
        /// <param name="json"></param>
        /// <returns></returns>
        public PageModel<IFP_UM_ROLE> SelectRole(JsonElement json)
        {
            var filter = json.GetValue<IList<IFP_UM_ROLE>>("filter");
            var paging = json.GetValue<PageModel>("paging");
            var result = _service.Value.SelectRole(filter, paging);
            return result;
        }

        public string UserRoleList(JsonElement obj)
        {
            //IFP_UM_USER_INFO param = JsonConvert.DeserializeObject<IFP_UM_USER_INFO>(obj.ToString());

            string gid = obj.GetValue<string>("Gid");

            return _service.Value.UserRoleList(gid);
        }

        public object UpdateUserRole(JsonElement obj)
        {
            //IFP_UM_USER_INFO param = JsonConvert.DeserializeObject<IFP_UM_USER_INFO>(obj.ToString());
            string gid = obj.GetValue<string>("Gid");
            string roleIds = obj.GetValue<string>("RoleIds");

            return _service.Value.UpdateUserRole(gid, roleIds);
        }
        public PFActionResult DelRoleByGid(JsonElement json)
        {
            var obj = json.GetValue<IFP_UM_ROLE>();
            string gid = obj.Gid.Value;
            return _service.Value.DelRoleByGid(gid);
        }

        public PFActionResult SaveRole(JsonElement json)
        {
            // 创建序列化设置并添加Newtonsoft的JSON转换器
            var settings = new JsonSerializerSettings
            {
                Converters = new List<JsonConverter> { new NewtonsoftFieldConverter() }
            };

            IFP_UM_ROLE param = JsonConvert.DeserializeObject<IFP_UM_ROLE>(json.ToString(), settings);
            return _service.Value.SaveRole(param);
        }

        /// <summary>
        /// 原前端框架 KJ 管控调用
        /// </summary>
        /// <param name="json"></param>
        /// <returns></returns>
        public PFActionResult SaveRoleMenue(JsonElement json)
        {
            // 创建序列化设置并添加Newtonsoft的JSON转换器
            var settings = new JsonSerializerSettings
            {
                Converters = new List<JsonConverter> { new NewtonsoftFieldConverter() }
            };

            IFP_UM_ROLE_MENU obj = JsonConvert.DeserializeObject<IFP_UM_ROLE_MENU>(json.ToString(), settings);

            return _service.Value.SaveRoleMenue(obj.Gid.Value, obj.MenuGidList, obj.HiddenBtnGidList);
        }

        /// <summary>
        /// 新前端框架Pure Admin调用（保持向后兼容）
        /// </summary>
        /// <param name="json">角色菜单权限数据</param>
        /// <returns>保存结果</returns>
        public PFActionResult SaveRoleMenu(JsonElement json)
        {
            try
            {
                // 方式1：直接解析参数（推荐）
                if (json.TryGetProperty("roleGid", out JsonElement roleGidElement) &&
                    json.TryGetProperty("menuGids", out JsonElement menuGidsElement) &&
                    json.TryGetProperty("hiddenBtns", out JsonElement hiddenBtnsElement))
                {
                    string roleGid = roleGidElement.GetString();
                    List<string> menuGids = System.Text.Json.JsonSerializer.Deserialize<List<string>>(menuGidsElement.GetRawText());
                    List<string> hiddenBtns = System.Text.Json.JsonSerializer.Deserialize<List<string>>(hiddenBtnsElement.GetRawText());

                    // 检查是否有权限同步标志（可选参数，默认true）
                    bool enablePermissionSync = true;
                    if (json.TryGetProperty("enablePermissionSync", out JsonElement syncElement))
                    {
                        enablePermissionSync = syncElement.GetBoolean();
                    }

                    return _service.Value.SaveRoleMenu(roleGid, menuGids, hiddenBtns, enablePermissionSync);
                }

                // 方式2：兼容原有格式
                var settings = new JsonSerializerSettings
                {
                    Converters = new List<JsonConverter> { new NewtonsoftFieldConverter() }
                };

                // 尝试解析为 IFP_UM_ROLE_MENU 对象
                IFP_UM_ROLE_MENU obj = JsonConvert.DeserializeObject<IFP_UM_ROLE_MENU>(json.ToString(), settings);

                // 确保关键字段不为空
                if (string.IsNullOrEmpty(obj.RoleGuid.Value))
                {
                    return new PFActionResult
                    {
                        success = false,
                        msg = "角色GUID不能为空"
                    };
                }

                if (obj.MenuGidList == null)
                {
                    obj.MenuGidList = new List<string>();
                }

                if (obj.HiddenBtnGidList == null)
                {
                    obj.HiddenBtnGidList = new List<string>();
                }

                // 默认启用权限同步
                return _service.Value.SaveRoleMenue(obj.RoleGuid.Value, obj.MenuGidList, obj.HiddenBtnGidList);
            }
            catch (Exception ex)
            {
                return new PFActionResult
                {
                    success = false,
                    msg = $"参数解析失败：{ex.Message}"
                };
            }
        }
        public List<string> QueryRoleBtnGid(JsonElement json)
        {
            string roleGid = json.GetValue<string>("roleGid");
            string menuUrl = json.GetValue<string>("menuUrl");
            return _service.Value.QueryRoleBtnGid(roleGid, menuUrl);
        }

        /// <summary>
        ///  拿到超级管理员角色id
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public PFActionResult SuperAdmin(JsonElement json)
        {
            return new PFActionResult()
            {
                data = _service.Value.SuperAdmin()
            };
        }
        /// <summary>
        /// 保存RoleList，存查样柜使用，先删除所有角色再添加！！！！
        /// </summary>
        /// <param name="searchVO"></param>
        /// <returns></returns>
        public PFActionResult RoleListSave(JsonElement json)
        {
            // 创建序列化设置并添加Newtonsoft的JSON转换器
            var settings = new JsonSerializerSettings
            {
                Converters = new List<JsonConverter> { new NewtonsoftFieldConverter() }
            };

            List<IFP_UM_ROLE> list = JsonConvert.DeserializeObject<List<IFP_UM_ROLE>>(json.ToString(), settings);
            return _service.Value.RoleListSave(list);
        }

        #region 新增按钮权限管理API

        /// <summary>
        /// 获取页面按钮列表
        /// </summary>
        /// <param name="json">{"pageUrl": "/system/user"}</param>
        /// <returns>按钮列表</returns>
        public PFActionResult GetPageButtons(JsonElement json)
        {
            try
            {
                string pageUrl = json.GetValue<string>("pageUrl");

                if (string.IsNullOrWhiteSpace(pageUrl))
                {
                    return new PFActionResult
                    {
                        success = false,
                        msg = "页面URL不能为空"
                    };
                }

                var buttons = _service.Value.GetPageButtons(pageUrl);
                return new PFActionResult
                {
                    success = true,
                    data = buttons
                };
            }
            catch (Exception ex)
            {
                return new PFActionResult
                {
                    success = false,
                    msg = $"获取页面按钮失败：{ex.Message}"
                };
            }
        }

        /// <summary>
        /// 获取角色按钮权限配置
        /// </summary>
        /// <param name="json">{"roleGid": "role0001", "pageUrl": "/system/user"}</param>
        /// <returns>角色按钮权限配置</returns>
        public PFActionResult GetRoleButtonPermissions(JsonElement json)
        {
            try
            {
                string roleGid = json.GetValue<string>("roleGid");
                string pageUrl = json.TryGetProperty("pageUrl", out JsonElement pageUrlElement)
                    ? pageUrlElement.GetString() : "";

                if (string.IsNullOrWhiteSpace(roleGid))
                {
                    return new PFActionResult
                    {
                        success = false,
                        msg = "角色ID不能为空"
                    };
                }

                var permissions = _service.Value.GetRoleButtonPermissions(roleGid, pageUrl);
                return new PFActionResult
                {
                    success = true,
                    data = permissions
                };
            }
            catch (Exception ex)
            {
                return new PFActionResult
                {
                    success = false,
                    msg = $"获取角色按钮权限失败：{ex.Message}"
                };
            }
        }

        /// <summary>
        /// 保存角色按钮权限配置
        /// </summary>
        /// <param name="json">{"roleGid": "role0001", "permissions": [...]}</param>
        /// <returns>保存结果</returns>
        public PFActionResult SaveRoleButtonPermissions(JsonElement json)
        {
            try
            {
                var settings = new JsonSerializerSettings
                {
                    Converters = new List<JsonConverter> { new NewtonsoftFieldConverter() }
                };

                var requestData = JsonConvert.DeserializeObject<RoleButtonPermissionRequest>(json.ToString(), settings);

                if (string.IsNullOrWhiteSpace(requestData.RoleGid))
                {
                    return new PFActionResult
                    {
                        success = false,
                        msg = "角色ID不能为空"
                    };
                }

                var result = _service.Value.SaveRoleButtonPermissions(requestData.RoleGid, requestData.Permissions);
                return result;
            }
            catch (Exception ex)
            {
                return new PFActionResult
                {
                    success = false,
                    msg = $"保存角色按钮权限失败：{ex.Message}"
                };
            }
        }

        /// <summary>
        /// 获取用户按钮权限
        /// </summary>
        /// <param name="json">{"userGid": "admin", "pageUrl": "/system/user"}</param>
        /// <returns>用户按钮权限列表</returns>
        public PFActionResult GetUserButtonPermissions(JsonElement json)
        {
            try
            {
                string userGid = json.TryGetProperty("userGid", out JsonElement userGidElement)
                    ? userGidElement.GetString() : UserCache.GetUserID();
                string pageUrl = json.TryGetProperty("pageUrl", out JsonElement pageUrlElement)
                    ? pageUrlElement.GetString() : "";

                if (string.IsNullOrWhiteSpace(userGid))
                {
                    return new PFActionResult
                    {
                        success = false,
                        msg = "用户ID不能为空"
                    };
                }

                var permissions = _service.Value.GetUserButtonPermissions(userGid, pageUrl);
                return new PFActionResult
                {
                    success = true,
                    data = permissions
                };
            }
            catch (Exception ex)
            {
                return new PFActionResult
                {
                    success = false,
                    msg = $"获取用户按钮权限失败：{ex.Message}"
                };
            }
        }

        /// <summary>
        /// 管理系统按钮定义
        /// </summary>
        /// <param name="json">{"action": "list|add|update|delete", "data": {...}}</param>
        /// <returns>操作结果</returns>
        public PFActionResult ManageSystemButtons(JsonElement json)
        {
            try
            {
                string action = json.GetValue<string>("action");

                switch (action.ToLower())
                {
                    case "list":
                        var buttons = _service.Value.GetAllSystemButtons();
                        return new PFActionResult { success = true, data = buttons };

                    case "add":
                    case "update":
                        var settings = new JsonSerializerSettings
                        {
                            Converters = new List<JsonConverter> { new NewtonsoftFieldConverter() }
                        };
                        var buttonData = JsonConvert.DeserializeObject<IFP_SYSTEM_BUTTONS>(
                            json.GetProperty("data").ToString(), settings);

                        var saveResult = _service.Value.SaveSystemButton(buttonData);
                        return saveResult;

                    case "delete":
                        string buttonGid = json.GetProperty("data").GetProperty("gid").GetString();
                        var deleteResult = _service.Value.DeleteSystemButton(buttonGid);
                        return deleteResult;

                    default:
                        return new PFActionResult
                        {
                            success = false,
                            msg = "不支持的操作类型"
                        };
                }
            }
            catch (Exception ex)
            {
                return new PFActionResult
                {
                    success = false,
                    msg = $"管理系统按钮失败：{ex.Message}"
                };
            }
        }

        /// <summary>
        /// 批量导入页面按钮
        /// </summary>
        /// <param name="json">{"pageUrl": "/system/user", "buttons": [...]}</param>
        /// <returns>导入结果</returns>
        public PFActionResult ImportPageButtons(JsonElement json)
        {
            try
            {
                string pageUrl = json.GetValue<string>("pageUrl");
                var buttons = json.GetProperty("buttons");

                if (string.IsNullOrWhiteSpace(pageUrl))
                {
                    return new PFActionResult
                    {
                        success = false,
                        msg = "页面URL不能为空"
                    };
                }

                var buttonList = new List<IFP_SYSTEM_BUTTONS>();
                foreach (var button in buttons.EnumerateArray())
                {
                    var buttonData = new IFP_SYSTEM_BUTTONS
                    {
                        Gid = Guid.NewGuid().ToString("N"),
                        ButtonId = button.GetProperty("buttonId").GetString(),
                        ButtonName = button.GetProperty("buttonName").GetString(),
                        PageUrl = pageUrl,
                        PageName = button.TryGetProperty("pageName", out var pageNameElement)
                            ? pageNameElement.GetString() : "未知页面",
                        ButtonGroup = button.TryGetProperty("buttonGroup", out var groupElement)
                            ? groupElement.GetString() : "默认",
                        ButtonType = button.TryGetProperty("buttonType", out var typeElement)
                            ? typeElement.GetString() : "primary",
                        Icon = button.TryGetProperty("icon", out var iconElement)
                            ? iconElement.GetString() : "",
                        Description = button.TryGetProperty("description", out var descElement)
                            ? descElement.GetString() : "",
                        Sort = button.TryGetProperty("sort", out var sortElement)
                            ? sortElement.GetInt32() : 999,
                        IsEnabled = 1,
                        CreateTime = DateTime.Now,
                        Creator = UserCache.GetUserID(),
                        Delt = 0
                    };

                    buttonList.Add(buttonData);
                }

                var result = _service.Value.BatchSaveSystemButtons(buttonList);
                return result;
            }
            catch (Exception ex)
            {
                return new PFActionResult
                {
                    success = false,
                    msg = $"批量导入按钮失败：{ex.Message}"
                };
            }
        }

        #endregion

        #region 权限继承同步API

        /// <summary>
        /// 同步角色权限到用户
        /// </summary>
        /// <param name="json">{"roleGid": "role001"}</param>
        /// <returns>同步结果</returns>
        public PFActionResult SyncRolePermissionsToUsers(JsonElement json)
        {
            try
            {
                string roleGid = json.GetValue<string>("roleGid");

                if (string.IsNullOrWhiteSpace(roleGid))
                {
                    return new PFActionResult
                    {
                        success = false,
                        msg = "角色ID不能为空"
                    };
                }

                return _service.Value.SyncUserPermissionsFromRole(roleGid);
            }
            catch (Exception ex)
            {
                return new PFActionResult
                {
                    success = false,
                    msg = $"同步角色权限失败：{ex.Message}"
                };
            }
        }

        /// <summary>
        /// 批量同步多个角色权限到用户
        /// </summary>
        /// <param name="json">{"roleGids": ["role001", "role002"]}</param>
        /// <returns>同步结果</returns>
        public PFActionResult BatchSyncRolePermissionsToUsers(JsonElement json)
        {
            try
            {
                var roleGids = json.GetProperty("roleGids").EnumerateArray()
                    .Select(x => x.GetString()).ToList();

                if (roleGids == null || roleGids.Count == 0)
                {
                    return new PFActionResult
                    {
                        success = false,
                        msg = "请指定要同步的角色ID列表"
                    };
                }

                return _service.Value.BatchSyncUserPermissionsFromRoles(roleGids);
            }
            catch (Exception ex)
            {
                return new PFActionResult
                {
                    success = false,
                    msg = $"批量同步角色权限失败：{ex.Message}"
                };
            }
        }

        /// <summary>
        /// 获取权限继承关系报告
        /// </summary>
        /// <param name="json">{"userGid": "admin"} 或 {} 查询所有用户</param>
        /// <returns>权限继承报告</returns>
        public PFActionResult GetPermissionInheritanceReport(JsonElement json)
        {
            try
            {
                string userGid = json.TryGetProperty("userGid", out JsonElement userGidElement)
                    ? userGidElement.GetString() : "";

                return _service.Value.GetPermissionInheritanceReport(userGid);
            }
            catch (Exception ex)
            {
                return new PFActionResult
                {
                    success = false,
                    msg = $"获取权限继承报告失败：{ex.Message}"
                };
            }
        }

        #endregion

        /// <summary>
        /// 更新用户角色（增强版，支持权限同步）
        /// </summary>
        /// <param name="obj">
        /// {
        ///   "Gid": "用户ID", 
        ///   "RoleIds": "角色ID列表(逗号分隔)",
        ///   "EnablePermissionSync": true|false (可选，默认true)
        /// }
        /// </param>
        /// <returns>更新结果</returns>
        public PFActionResult UpdateUserRoleEnhanced(JsonElement obj)
        {
            try
            {
                string gid = obj.GetValue<string>("Gid");
                string roleIds = obj.GetValue<string>("RoleIds");

                // 获取是否启用权限同步标志，默认为true
                bool enablePermissionSync = true;
                if (obj.TryGetProperty("EnablePermissionSync", out JsonElement syncElement))
                {
                    enablePermissionSync = syncElement.GetBoolean();
                }

                if (string.IsNullOrWhiteSpace(gid))
                {
                    return new PFActionResult
                    {
                        success = false,
                        msg = "用户ID不能为空"
                    };
                }

                return _service.Value.UpdateUserRoleEnhanced(gid, roleIds, enablePermissionSync);
            }
            catch (Exception ex)
            {
                return new PFActionResult
                {
                    success = false,
                    msg = $"更新用户角色失败：{ex.Message}"
                };
            }
        }
    }
}
